Manifest-Version: 1.0
Bundle-License: http://www.apache.org/licenses/LICENSE-2.0.txt
Bundle-SymbolicName: org.apache.ignite.ignite-core
Archiver-Version: Plexus Archiver
Built-By: xz
Bnd-LastModified: 1728436859022
Specification-Title: ignite-core
Implementation-Vendor-Id: org.apache.ignite
Bundle-DocURL: http://ignite.apache.org
Import-Package: com.sun.management,javax.cache;version="[1.0,2)",javax
 .cache.configuration,javax.cache.event;version="[1.0,2)",javax.cache.
 expiry,javax.cache.integration,javax.cache.management,javax.cache.pro
 cessor,javax.cache.spi;version="[1.0,2)",javax.crypto,javax.crypto.sp
 ec,javax.imageio,javax.management,javax.management.openmbean,javax.na
 ming,javax.net.ssl,javax.sql,javax.swing,javax.swing.border,org.apach
 e.ignite,org.apache.ignite.binary,org.apache.ignite.cache,org.apache.
 ignite.cache.affinity,org.apache.ignite.cache.affinity.rendezvous,org
 .apache.ignite.cache.eviction,org.apache.ignite.cache.eviction.fifo,o
 rg.apache.ignite.cache.eviction.lru,org.apache.ignite.cache.query,org
 .apache.ignite.cache.query.annotations,org.apache.ignite.cache.store,
 org.apache.ignite.cache.store.jdbc,org.apache.ignite.cache.store.jdbc
 .dialect,org.apache.ignite.client,org.apache.ignite.client.util,org.a
 pache.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.comp
 ute.gridify,org.apache.ignite.compute.gridify.aop,org.apache.ignite.c
 onfiguration,org.apache.ignite.events,org.apache.ignite.failure,org.a
 pache.ignite.internal,org.apache.ignite.internal.binary,org.apache.ig
 nite.internal.binary.builder,org.apache.ignite.internal.binary.stream
 s,org.apache.ignite.internal.client,org.apache.ignite.internal.client
 .balancer,org.apache.ignite.internal.client.impl,org.apache.ignite.in
 ternal.client.impl.connection,org.apache.ignite.internal.client.marsh
 aller,org.apache.ignite.internal.client.marshaller.jdk,org.apache.ign
 ite.internal.client.marshaller.optimized,org.apache.ignite.internal.c
 lient.router,org.apache.ignite.internal.client.router.impl,org.apache
 .ignite.internal.client.ssl,org.apache.ignite.internal.client.thin,or
 g.apache.ignite.internal.client.thin.io,org.apache.ignite.internal.cl
 ient.thin.io.gridnioserver,org.apache.ignite.internal.client.util,org
 .apache.ignite.internal.cluster,org.apache.ignite.internal.cluster.gr
 aph,org.apache.ignite.internal.commandline.meta.subcommands,org.apach
 e.ignite.internal.commandline.property,org.apache.ignite.internal.com
 pute,org.apache.ignite.internal.cust.affinityep.processors.cache,org.
 apache.ignite.internal.cust.affinityep.processors.cache.client,org.ap
 ache.ignite.internal.cust.affinityep.processors.cache.client.context,
 org.apache.ignite.internal.cust.affinityep.processors.cache.client.ut
 il,org.apache.ignite.internal.cust.affinityep.processors.cache.dhtato
 mic.future,org.apache.ignite.internal.cust.affinityep.processors.cach
 e.dhtatomic.message,org.apache.ignite.internal.cust.affinityep.proces
 sors.cache.dhtatomic.result,org.apache.ignite.internal.cust.affinitye
 p.processors.cache.dhtatomic.result.triple,org.apache.ignite.internal
 .cust.affinityep.processors.cache.exception,org.apache.ignite.interna
 l.cust.affinityep.processors.cache.server,org.apache.ignite.internal.
 cust.affinityep.processors.cache.server.context,org.apache.ignite.int
 ernal.cust.affinityep.processors.metrics,org.apache.ignite.internal.c
 ust.affinityep.processors.metrics.impl,org.apache.ignite.internal.cus
 t.affinityep.processors.util,org.apache.ignite.internal.cust.affinity
 ep.processors.util.typedef,org.apache.ignite.internal.cust.common.nod
 e,org.apache.ignite.internal.cust.demo.entity,org.apache.ignite.inter
 nal.cust.server,org.apache.ignite.internal.cust.server.base,org.apach
 e.ignite.internal.cust.server.base.metrics,org.apache.ignite.internal
 .cust.server.cache,org.apache.ignite.internal.cust.server.keyanalysis
 ,org.apache.ignite.internal.cust.server.keyanalysis.bigkey,org.apache
 .ignite.internal.cust.server.keyanalysis.bigkey.config,org.apache.ign
 ite.internal.cust.server.keyanalysis.bigkey.metrics,org.apache.ignite
 .internal.cust.server.keyanalysis.bigkey.metrics.impl,org.apache.igni
 te.internal.cust.server.keyanalysis.bigkey.mr.change,org.apache.ignit
 e.internal.cust.server.keyanalysis.bigkey.mr.clean,org.apache.ignite.
 internal.cust.server.keyanalysis.bigkey.mr.data,org.apache.ignite.int
 ernal.cust.server.keyanalysis.bigkey.mr.state,org.apache.ignite.inter
 nal.cust.server.keyanalysis.bigkey.util,org.apache.ignite.internal.cu
 st.server.keyanalysis.hotkey,org.apache.ignite.internal.cust.server.k
 eyanalysis.hotkey.config,org.apache.ignite.internal.cust.server.keyan
 alysis.hotkey.metrics,org.apache.ignite.internal.cust.server.keyanaly
 sis.hotkey.metrics.impl,org.apache.ignite.internal.cust.server.keyana
 lysis.hotkey.mr.change,org.apache.ignite.internal.cust.server.keyanal
 ysis.hotkey.mr.clean,org.apache.ignite.internal.cust.server.keyanalys
 is.hotkey.mr.data,org.apache.ignite.internal.cust.server.keyanalysis.
 hotkey.mr.state,org.apache.ignite.internal.cust.server.keyanalysis.ra
 telimit,org.apache.ignite.internal.cust.server.keyanalysis.slowkey,or
 g.apache.ignite.internal.cust.server.keyanalysis.slowkey.config,org.a
 pache.ignite.internal.cust.server.keyanalysis.slowkey.metrics,org.apa
 che.ignite.internal.cust.server.keyanalysis.slowkey.metrics.impl,org.
 apache.ignite.internal.cust.server.keyanalysis.slowkey.mr.change,org.
 apache.ignite.internal.cust.server.keyanalysis.slowkey.mr.clean,org.a
 pache.ignite.internal.cust.server.keyanalysis.slowkey.mr.data,org.apa
 che.ignite.internal.cust.server.keyanalysis.slowkey.mr.state,org.apac
 he.ignite.internal.cust.server.keyanalysis.slowkey.util,org.apache.ig
 nite.internal.cust.server.modulecheck,org.apache.ignite.internal.cust
 .server.modulecheck.entity,org.apache.ignite.internal.cust.server.mod
 ulecheck.metric,org.apache.ignite.internal.cust.server.modulecheck.mr
 .state,org.apache.ignite.internal.cust.server.node,org.apache.ignite.
 internal.cust.server.node.link,org.apache.ignite.internal.cust.server
 .node.link.check,org.apache.ignite.internal.cust.server.node.link.ent
 ity,org.apache.ignite.internal.cust.server.node.link.metrics,org.apac
 he.ignite.internal.cust.server.node.link.mr.messageip.change,org.apac
 he.ignite.internal.cust.server.node.link.util,org.apache.ignite.inter
 nal.cust.server.node.storage.entity,org.apache.ignite.internal.cust.s
 erver.performance,org.apache.ignite.internal.cust.server.performance.
 ep,org.apache.ignite.internal.cust.server.performance.ep.entity,org.a
 pache.ignite.internal.cust.server.performance.ep.metric,org.apache.ig
 nite.internal.cust.server.performance.ep.metric.impl,org.apache.ignit
 e.internal.cust.server.performance.ep.mr.deploystate,org.apache.ignit
 e.internal.cust.server.performance.ep.mr.performance,org.apache.ignit
 e.internal.cust.server.simplehelper,org.apache.ignite.internal.cust.s
 erver.stript,org.apache.ignite.internal.cust.server.stript.entity,org
 .apache.ignite.internal.cust.server.stript.mr.queueinfo,org.apache.ig
 nite.internal.cust.thin.base,org.apache.ignite.internal.cust.thin.cli
 ent.external,org.apache.ignite.internal.cust.thin.client.internal,org
 .apache.ignite.internal.cust.thin.metrics,org.apache.ignite.internal.
 cust.thin.metrics.type,org.apache.ignite.internal.cust.thin.metrics.t
 ype.cacheep.entity,org.apache.ignite.internal.cust.thin.metrics.type.
 cacheep.ep,org.apache.ignite.internal.cust.thin.metrics.type.cacheop.
 entity,org.apache.ignite.internal.cust.thin.metrics.type.cacheop.op,o
 rg.apache.ignite.internal.cust.thin.metrics.type.single,org.apache.ig
 nite.internal.cust.thin.metrics.type.single.affinityep,org.apache.ign
 ite.internal.cust.thin.metrics.type.single.tx,org.apache.ignite.inter
 nal.direct,org.apache.ignite.internal.direct.state,org.apache.ignite.
 internal.direct.stream,org.apache.ignite.internal.direct.stream.v1,or
 g.apache.ignite.internal.direct.stream.v2,org.apache.ignite.internal.
 direct.stream.v3,org.apache.ignite.internal.dto,org.apache.ignite.int
 ernal.events,org.apache.ignite.internal.executor,org.apache.ignite.in
 ternal.jdbc,org.apache.ignite.internal.jdbc.thin,org.apache.ignite.in
 ternal.jdbc2,org.apache.ignite.internal.logger.platform,org.apache.ig
 nite.internal.maintenance,org.apache.ignite.internal.managers,org.apa
 che.ignite.internal.managers.checkpoint,org.apache.ignite.internal.ma
 nagers.collision,org.apache.ignite.internal.managers.communication,or
 g.apache.ignite.internal.managers.deployment,org.apache.ignite.intern
 al.managers.deployment.protocol.gg,org.apache.ignite.internal.manager
 s.discovery,org.apache.ignite.internal.managers.encryption,org.apache
 .ignite.internal.managers.eventstorage,org.apache.ignite.internal.man
 agers.failover,org.apache.ignite.internal.managers.indexing,org.apach
 e.ignite.internal.managers.loadbalancer,org.apache.ignite.internal.ma
 nagers.systemview,org.apache.ignite.internal.managers.systemview.walk
 er,org.apache.ignite.internal.managers.tracing,org.apache.ignite.inte
 rnal.marshaller.optimized,org.apache.ignite.internal.mem,org.apache.i
 gnite.internal.mem.file,org.apache.ignite.internal.mem.unsafe,org.apa
 che.ignite.internal.metric,org.apache.ignite.internal.mxbean,org.apac
 he.ignite.internal.pagemem,org.apache.ignite.internal.pagemem.impl,or
 g.apache.ignite.internal.pagemem.store,org.apache.ignite.internal.pag
 emem.wal,org.apache.ignite.internal.pagemem.wal.record,org.apache.ign
 ite.internal.pagemem.wal.record.delta,org.apache.ignite.internal.proc
 essors,org.apache.ignite.internal.processors.affinity,org.apache.igni
 te.internal.processors.authentication,org.apache.ignite.internal.proc
 essors.bulkload,org.apache.ignite.internal.processors.bulkload.pipeli
 ne,org.apache.ignite.internal.processors.cache,org.apache.ignite.inte
 rnal.processors.cache.affinity,org.apache.ignite.internal.processors.
 cache.binary,org.apache.ignite.internal.processors.cache.datastructur
 es,org.apache.ignite.internal.processors.cache.distributed,org.apache
 .ignite.internal.processors.cache.distributed.dht,org.apache.ignite.i
 nternal.processors.cache.distributed.dht.atomic,org.apache.ignite.int
 ernal.processors.cache.distributed.dht.colocated,org.apache.ignite.in
 ternal.processors.cache.distributed.dht.preloader,org.apache.ignite.i
 nternal.processors.cache.distributed.dht.preloader.latch,org.apache.i
 gnite.internal.processors.cache.distributed.dht.topology,org.apache.i
 gnite.internal.processors.cache.distributed.near,org.apache.ignite.in
 ternal.processors.cache.distributed.near.consistency,org.apache.ignit
 e.internal.processors.cache.dr,org.apache.ignite.internal.processors.
 cache.extras,org.apache.ignite.internal.processors.cache.jta,org.apac
 he.ignite.internal.processors.cache.local,org.apache.ignite.internal.
 processors.cache.local.atomic,org.apache.ignite.internal.processors.c
 ache.mvcc,org.apache.ignite.internal.processors.cache.mvcc.msg,org.ap
 ache.ignite.internal.processors.cache.mvcc.txlog,org.apache.ignite.in
 ternal.processors.cache.persistence,org.apache.ignite.internal.proces
 sors.cache.persistence.checkpoint,org.apache.ignite.internal.processo
 rs.cache.persistence.defragmentation,org.apache.ignite.internal.proce
 ssors.cache.persistence.defragmentation.maintenance,org.apache.ignite
 .internal.processors.cache.persistence.diagnostic.pagelocktracker,org
 .apache.ignite.internal.processors.cache.persistence.diagnostic.pagel
 ocktracker.dumpprocessors,org.apache.ignite.internal.processors.cache
 .persistence.diagnostic.pagelocktracker.log,org.apache.ignite.interna
 l.processors.cache.persistence.diagnostic.pagelocktracker.stack,org.a
 pache.ignite.internal.processors.cache.persistence.diagnostic.pageloc
 ktracker.store,org.apache.ignite.internal.processors.cache.persistenc
 e.evict,org.apache.ignite.internal.processors.cache.persistence.file,
 org.apache.ignite.internal.processors.cache.persistence.filename,org.
 apache.ignite.internal.processors.cache.persistence.freelist,org.apac
 he.ignite.internal.processors.cache.persistence.freelist.io,org.apach
 e.ignite.internal.processors.cache.persistence.metastorage,org.apache
 .ignite.internal.processors.cache.persistence.metastorage.pendingtask
 ,org.apache.ignite.internal.processors.cache.persistence.pagemem,org.
 apache.ignite.internal.processors.cache.persistence.partstate,org.apa
 che.ignite.internal.processors.cache.persistence.partstorage,org.apac
 he.ignite.internal.processors.cache.persistence.snapshot,org.apache.i
 gnite.internal.processors.cache.persistence.tree,org.apache.ignite.in
 ternal.processors.cache.persistence.tree.io,org.apache.ignite.interna
 l.processors.cache.persistence.tree.reuse,org.apache.ignite.internal.
 processors.cache.persistence.tree.util,org.apache.ignite.internal.pro
 cessors.cache.persistence.wal,org.apache.ignite.internal.processors.c
 ache.persistence.wal.aware,org.apache.ignite.internal.processors.cach
 e.persistence.wal.crc,org.apache.ignite.internal.processors.cache.per
 sistence.wal.filehandle,org.apache.ignite.internal.processors.cache.p
 ersistence.wal.io,org.apache.ignite.internal.processors.cache.persist
 ence.wal.reader,org.apache.ignite.internal.processors.cache.persisten
 ce.wal.record,org.apache.ignite.internal.processors.cache.persistence
 .wal.scanner,org.apache.ignite.internal.processors.cache.persistence.
 wal.serializer,org.apache.ignite.internal.processors.cache.persistenc
 e.wal.serializer.io,org.apache.ignite.internal.processors.cache.query
 ,org.apache.ignite.internal.processors.cache.query.continuous,org.apa
 che.ignite.internal.processors.cache.store,org.apache.ignite.internal
 .processors.cache.transactions,org.apache.ignite.internal.processors.
 cache.tree,org.apache.ignite.internal.processors.cache.tree.mvcc.data
 ,org.apache.ignite.internal.processors.cache.tree.mvcc.search,org.apa
 che.ignite.internal.processors.cache.verify,org.apache.ignite.interna
 l.processors.cache.version,org.apache.ignite.internal.processors.cach
 e.warmup,org.apache.ignite.internal.processors.cacheobject,org.apache
 .ignite.internal.processors.closure,org.apache.ignite.internal.proces
 sors.cluster,org.apache.ignite.internal.processors.cluster.baseline.a
 utoadjust,org.apache.ignite.internal.processors.compress,org.apache.i
 gnite.internal.processors.configuration.distributed,org.apache.ignite
 .internal.processors.continuous,org.apache.ignite.internal.processors
 .datastreamer,org.apache.ignite.internal.processors.datastructures,or
 g.apache.ignite.internal.processors.diagnostic,org.apache.ignite.inte
 rnal.processors.dr,org.apache.ignite.internal.processors.failure,org.
 apache.ignite.internal.processors.job,org.apache.ignite.internal.proc
 essors.jobmetrics,org.apache.ignite.internal.processors.localtask,org
 .apache.ignite.internal.processors.marshaller,org.apache.ignite.inter
 nal.processors.metastorage,org.apache.ignite.internal.processors.meta
 storage.persistence,org.apache.ignite.internal.processors.metric,org.
 apache.ignite.internal.processors.metric.impl,org.apache.ignite.inter
 nal.processors.nodevalidation,org.apache.ignite.internal.processors.o
 dbc,org.apache.ignite.internal.processors.odbc.jdbc,org.apache.ignite
 .internal.processors.odbc.odbc,org.apache.ignite.internal.processors.
 odbc.odbc.escape,org.apache.ignite.internal.processors.performancesta
 tistics,org.apache.ignite.internal.processors.platform,org.apache.ign
 ite.internal.processors.platform.binary,org.apache.ignite.internal.pr
 ocessors.platform.cache,org.apache.ignite.internal.processors.platfor
 m.cache.affinity,org.apache.ignite.internal.processors.platform.cache
 .expiry,org.apache.ignite.internal.processors.platform.cache.query,or
 g.apache.ignite.internal.processors.platform.cache.store,org.apache.i
 gnite.internal.processors.platform.callback,org.apache.ignite.interna
 l.processors.platform.client,org.apache.ignite.internal.processors.pl
 atform.client.binary,org.apache.ignite.internal.processors.platform.c
 lient.cache,org.apache.ignite.internal.processors.platform.client.clu
 ster,org.apache.ignite.internal.processors.platform.client.compute,or
 g.apache.ignite.internal.processors.platform.client.epproxy,org.apach
 e.ignite.internal.processors.platform.client.service,org.apache.ignit
 e.internal.processors.platform.client.tx,org.apache.ignite.internal.p
 rocessors.platform.cluster,org.apache.ignite.internal.processors.plat
 form.compute,org.apache.ignite.internal.processors.platform.datastrea
 mer,org.apache.ignite.internal.processors.platform.datastructures,org
 .apache.ignite.internal.processors.platform.dotnet,org.apache.ignite.
 internal.processors.platform.entityframework,org.apache.ignite.intern
 al.processors.platform.events,org.apache.ignite.internal.processors.p
 latform.lifecycle,org.apache.ignite.internal.processors.platform.memo
 ry,org.apache.ignite.internal.processors.platform.message,org.apache.
 ignite.internal.processors.platform.messaging,org.apache.ignite.inter
 nal.processors.platform.plugin,org.apache.ignite.internal.processors.
 platform.plugin.cache,org.apache.ignite.internal.processors.platform.
 services,org.apache.ignite.internal.processors.platform.transactions,
 org.apache.ignite.internal.processors.platform.utils,org.apache.ignit
 e.internal.processors.platform.websession,org.apache.ignite.internal.
 processors.plugin,org.apache.ignite.internal.processors.pool,org.apac
 he.ignite.internal.processors.port,org.apache.ignite.internal.process
 ors.query,org.apache.ignite.internal.processors.query.h2.twostep.mess
 ages,org.apache.ignite.internal.processors.query.messages,org.apache.
 ignite.internal.processors.query.property,org.apache.ignite.internal.
 processors.query.schema,org.apache.ignite.internal.processors.query.s
 chema.message,org.apache.ignite.internal.processors.query.schema.oper
 ation,org.apache.ignite.internal.processors.resource,org.apache.ignit
 e.internal.processors.rest,org.apache.ignite.internal.processors.rest
 .client.message,org.apache.ignite.internal.processors.rest.handlers,o
 rg.apache.ignite.internal.processors.rest.handlers.auth,org.apache.ig
 nite.internal.processors.rest.handlers.beforeStart,org.apache.ignite.
 internal.processors.rest.handlers.cache,org.apache.ignite.internal.pr
 ocessors.rest.handlers.cluster,org.apache.ignite.internal.processors.
 rest.handlers.datastructures,org.apache.ignite.internal.processors.re
 st.handlers.log,org.apache.ignite.internal.processors.rest.handlers.m
 emory,org.apache.ignite.internal.processors.rest.handlers.probe,org.a
 pache.ignite.internal.processors.rest.handlers.query,org.apache.ignit
 e.internal.processors.rest.handlers.redis,org.apache.ignite.internal.
 processors.rest.handlers.redis.exception,org.apache.ignite.internal.p
 rocessors.rest.handlers.redis.key,org.apache.ignite.internal.processo
 rs.rest.handlers.redis.server,org.apache.ignite.internal.processors.r
 est.handlers.redis.string,org.apache.ignite.internal.processors.rest.
 handlers.task,org.apache.ignite.internal.processors.rest.handlers.top
 ,org.apache.ignite.internal.processors.rest.handlers.user,org.apache.
 ignite.internal.processors.rest.handlers.version,org.apache.ignite.in
 ternal.processors.rest.protocols,org.apache.ignite.internal.processor
 s.rest.protocols.tcp,org.apache.ignite.internal.processors.rest.proto
 cols.tcp.redis,org.apache.ignite.internal.processors.rest.request,org
 .apache.ignite.internal.processors.schedule,org.apache.ignite.interna
 l.processors.security,org.apache.ignite.internal.processors.security.
 sandbox,org.apache.ignite.internal.processors.segmentation,org.apache
 .ignite.internal.processors.service,org.apache.ignite.internal.proces
 sors.session,org.apache.ignite.internal.processors.subscription,org.a
 pache.ignite.internal.processors.task,org.apache.ignite.internal.proc
 essors.timeout,org.apache.ignite.internal.processors.tracing,org.apac
 he.ignite.internal.processors.tracing.configuration,org.apache.ignite
 .internal.processors.tracing.messages,org.apache.ignite.internal.sql,
 org.apache.ignite.internal.sql.command,org.apache.ignite.internal.sql
 .optimizer.affinity,org.apache.ignite.internal.suggestions,org.apache
 .ignite.internal.tracing,org.apache.ignite.internal.transactions,org.
 apache.ignite.internal.util,org.apache.ignite.internal.util.collectio
 n,org.apache.ignite.internal.util.distributed,org.apache.ignite.inter
 nal.util.function,org.apache.ignite.internal.util.future,org.apache.i
 gnite.internal.util.gridify,org.apache.ignite.internal.util.io,org.ap
 ache.ignite.internal.util.ipc,org.apache.ignite.internal.util.ipc.loo
 pback,org.apache.ignite.internal.util.ipc.shmem,org.apache.ignite.int
 ernal.util.lang,org.apache.ignite.internal.util.lang.gridfunc,org.apa
 che.ignite.internal.util.nio,org.apache.ignite.internal.util.nio.ssl,
 org.apache.ignite.internal.util.nodestart,org.apache.ignite.internal.
 util.offheap,org.apache.ignite.internal.util.offheap.unsafe,org.apach
 e.ignite.internal.util.spring,org.apache.ignite.internal.util.tostrin
 g,org.apache.ignite.internal.util.typedef,org.apache.ignite.internal.
 util.typedef.internal,org.apache.ignite.internal.util.worker,org.apac
 he.ignite.internal.visor,org.apache.ignite.internal.visor.annotation,
 org.apache.ignite.internal.visor.cache,org.apache.ignite.internal.vis
 or.compute,org.apache.ignite.internal.visor.event,org.apache.ignite.i
 nternal.visor.file,org.apache.ignite.internal.visor.igfs,org.apache.i
 gnite.internal.visor.log,org.apache.ignite.internal.visor.misc,org.ap
 ache.ignite.internal.visor.query,org.apache.ignite.internal.visor.tx,
 org.apache.ignite.internal.visor.util,org.apache.ignite.internal.viso
 r.verify,org.apache.ignite.internal.worker,org.apache.ignite.lang,org
 .apache.ignite.lifecycle,org.apache.ignite.logger,org.apache.ignite.l
 ogger.java,org.apache.ignite.maintenance,org.apache.ignite.marshaller
 ,org.apache.ignite.marshaller.jdk,org.apache.ignite.mxbean,org.apache
 .ignite.platform,org.apache.ignite.platform.cpp,org.apache.ignite.pla
 tform.dotnet,org.apache.ignite.plugin,org.apache.ignite.plugin.extens
 ions.communication,org.apache.ignite.plugin.platform,org.apache.ignit
 e.plugin.security,org.apache.ignite.plugin.segmentation,org.apache.ig
 nite.resources,org.apache.ignite.scheduler,org.apache.ignite.services
 ,org.apache.ignite.spi,org.apache.ignite.spi.checkpoint,org.apache.ig
 nite.spi.checkpoint.noop,org.apache.ignite.spi.collision,org.apache.i
 gnite.spi.collision.jobstealing,org.apache.ignite.spi.collision.noop,
 org.apache.ignite.spi.communication,org.apache.ignite.spi.communicati
 on.tcp,org.apache.ignite.spi.communication.tcp.internal,org.apache.ig
 nite.spi.communication.tcp.internal.shmem,org.apache.ignite.spi.commu
 nication.tcp.messages,org.apache.ignite.spi.deployment,org.apache.ign
 ite.spi.deployment.local,org.apache.ignite.spi.discovery,org.apache.i
 gnite.spi.discovery.isolated,org.apache.ignite.spi.discovery.tcp,org.
 apache.ignite.spi.discovery.tcp.internal,org.apache.ignite.spi.discov
 ery.tcp.ipfinder,org.apache.ignite.spi.discovery.tcp.ipfinder.multica
 st,org.apache.ignite.spi.discovery.tcp.ipfinder.vm,org.apache.ignite.
 spi.discovery.tcp.messages,org.apache.ignite.spi.encryption,org.apach
 e.ignite.spi.encryption.keystore,org.apache.ignite.spi.encryption.noo
 p,org.apache.ignite.spi.eventstorage,org.apache.ignite.spi.eventstora
 ge.memory,org.apache.ignite.spi.failover,org.apache.ignite.spi.failov
 er.always,org.apache.ignite.spi.indexing,org.apache.ignite.spi.indexi
 ng.noop,org.apache.ignite.spi.loadbalancing,org.apache.ignite.spi.loa
 dbalancing.roundrobin,org.apache.ignite.spi.metric,org.apache.ignite.
 spi.metric.jmx,org.apache.ignite.spi.metric.noop,org.apache.ignite.sp
 i.systemview,org.apache.ignite.spi.systemview.view,org.apache.ignite.
 spi.tracing,org.apache.ignite.ssl,org.apache.ignite.stream,org.apache
 .ignite.thread,org.apache.ignite.transactions,org.apache.ignite.util,
 org.apache.ignite.util.deque,org.jsr166;version="[1.0,2)",org.mindrot
 ,sun.misc,sun.nio.ch,javax.enterprise.util;resolution:=optional
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=1.8))"
Export-Package: org.apache.ignite;uses:="javax.cache,javax.cache.confi
 guration,javax.cache.expiry,javax.cache.processor,javax.sql,org.apach
 e.ignite.binary,org.apache.ignite.cache,org.apache.ignite.cache.affin
 ity,org.apache.ignite.cache.query,org.apache.ignite.client,org.apache
 .ignite.cluster,org.apache.ignite.compute,org.apache.ignite.configura
 tion,org.apache.ignite.events,org.apache.ignite.internal.cust.affinit
 yep.processors.cache.client,org.apache.ignite.internal.processors.clu
 ster.baseline.autoadjust,org.apache.ignite.internal.util.tostring,org
 .apache.ignite.lang,org.apache.ignite.mxbean,org.apache.ignite.plugin
 ,org.apache.ignite.scheduler,org.apache.ignite.services,org.apache.ig
 nite.spi.tracing,org.apache.ignite.stream,org.apache.ignite.transacti
 ons";version="2.10.0",org.apache.ignite.binary;uses:="org.apache.igni
 te";version="2.10.0",org.apache.ignite.cache;uses:="javax.cache,javax
 .cache.configuration,javax.cache.event,javax.cache.processor,javax.ca
 che.spi,org.apache.ignite,org.apache.ignite.internal.processors.cache
 ,org.apache.ignite.internal.processors.query.schema.operation,org.apa
 che.ignite.lang";version="2.10.0",org.apache.ignite.cache.affinity;us
 es:="org.apache.ignite.cluster,org.apache.ignite.events,org.apache.ig
 nite.internal.processors.affinity,org.apache.ignite.lang";version="2.
 10.0",org.apache.ignite.cache.affinity.rendezvous;uses:="org.apache.i
 gnite.cache.affinity,org.apache.ignite.cluster,org.apache.ignite.lang
 ";version="2.10.0",org.apache.ignite.cache.eviction;uses:="javax.cach
 e,javax.cache.configuration";version="2.10.0",org.apache.ignite.cache
 .eviction.fifo;uses:="org.apache.ignite.cache.eviction,org.apache.ign
 ite.mxbean";version="2.10.0",org.apache.ignite.cache.eviction.lru;use
 s:="org.apache.ignite.cache.eviction,org.apache.ignite.mxbean";versio
 n="2.10.0",org.apache.ignite.cache.eviction.sorted;uses:="org.apache.
 ignite.cache.eviction,org.apache.ignite.mxbean";version="2.10.0",org.
 apache.ignite.cache.query;uses:="javax.cache,javax.cache.configuratio
 n,javax.cache.event,org.apache.ignite,org.apache.ignite.cache,org.apa
 che.ignite.internal.processors.bulkload,org.apache.ignite.lang";versi
 on="2.10.0",org.apache.ignite.cache.query.annotations;version="2.10.0
 ",org.apache.ignite.cache.store;uses:="javax.cache,javax.cache.integr
 ation,org.apache.ignite.lang,org.apache.ignite.transactions";version=
 "2.10.0",org.apache.ignite.cache.store.jdbc;uses:="javax.cache,javax.
 cache.configuration,javax.cache.integration,javax.sql,org.apache.igni
 te,org.apache.ignite.cache.store,org.apache.ignite.cache.store.jdbc.d
 ialect,org.apache.ignite.lang,org.apache.ignite.lifecycle,org.apache.
 ignite.resources";version="2.10.0",org.apache.ignite.cache.store.jdbc
 .dialect;uses:="org.apache.ignite.internal.util.typedef";version="2.1
 0.0",org.apache.ignite.client;uses:="javax.cache.expiry,javax.cache.p
 rocessor,org.apache.ignite,org.apache.ignite.cache,org.apache.ignite.
 cache.query,org.apache.ignite.cluster,org.apache.ignite.internal.clie
 nt.thin,org.apache.ignite.internal.cust.affinityep.processors.util.ty
 pedef,org.apache.ignite.internal.cust.thin.client.external,org.apache
 .ignite.lang,org.apache.ignite.resources,org.apache.ignite.transactio
 ns";version="2.10.0",org.apache.ignite.client.util;uses:="org.apache.
 ignite.cluster";version="2.10.0",org.apache.ignite.cluster;uses:="org
 .apache.ignite,org.apache.ignite.lang";version="2.10.0",org.apache.ig
 nite.compute;uses:="org.apache.ignite,org.apache.ignite.cluster,org.a
 pache.ignite.lang";version="2.10.0",org.apache.ignite.compute.gridify
 ;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite
 .compute,org.apache.ignite.compute.gridify.aop,org.apache.ignite.lang
 ";version="2.10.0",org.apache.ignite.compute.gridify.aop;uses:="org.a
 pache.ignite,org.apache.ignite.cluster,org.apache.ignite.compute,org.
 apache.ignite.compute.gridify,org.apache.ignite.internal.util.gridify
 ,org.apache.ignite.internal.util.lang";version="2.10.0",org.apache.ig
 nite.configuration;uses:="javax.cache.configuration,javax.cache.expir
 y,javax.cache.integration,javax.management,javax.net.ssl,org.apache.i
 gnite,org.apache.ignite.binary,org.apache.ignite.cache,org.apache.ign
 ite.cache.affinity,org.apache.ignite.cache.eviction,org.apache.ignite
 .cache.store,org.apache.ignite.client,org.apache.ignite.cluster,org.a
 pache.ignite.events,org.apache.ignite.failure,org.apache.ignite.inter
 nal.client.ssl,org.apache.ignite.internal.cust.server.keyanalysis,org
 .apache.ignite.internal.processors.cache.persistence.file,org.apache.
 ignite.internal.util,org.apache.ignite.lang,org.apache.ignite.lifecyc
 le,org.apache.ignite.marshaller,org.apache.ignite.plugin,org.apache.i
 gnite.plugin.segmentation,org.apache.ignite.services,org.apache.ignit
 e.spi.checkpoint,org.apache.ignite.spi.collision,org.apache.ignite.sp
 i.communication,org.apache.ignite.spi.deployment,org.apache.ignite.sp
 i.discovery,org.apache.ignite.spi.encryption,org.apache.ignite.spi.ev
 entstorage,org.apache.ignite.spi.failover,org.apache.ignite.spi.index
 ing,org.apache.ignite.spi.loadbalancing,org.apache.ignite.spi.metric,
 org.apache.ignite.spi.systemview,org.apache.ignite.spi.tracing,org.ap
 ache.ignite.transactions";version="2.10.0",org.apache.ignite.events;u
 ses:="org.apache.ignite.cache,org.apache.ignite.cluster,org.apache.ig
 nite.compute,org.apache.ignite.internal.processors.tracing,org.apache
 .ignite.lang,org.apache.ignite.spi,org.apache.ignite.transactions";ve
 rsion="2.10.0",org.apache.ignite.failure;uses:="org.apache.ignite";ve
 rsion="2.10.0",org.apache.ignite.internal;uses:="javax.cache,javax.ma
 nagement,org.apache.ignite,org.apache.ignite.cache.affinity,org.apach
 e.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.configur
 ation,org.apache.ignite.events,org.apache.ignite.internal.binary,org.
 apache.ignite.internal.cluster,org.apache.ignite.internal.cust.affini
 tyep.processors.cache.client,org.apache.ignite.internal.cust.server,o
 rg.apache.ignite.internal.managers.checkpoint,org.apache.ignite.inter
 nal.managers.collision,org.apache.ignite.internal.managers.communicat
 ion,org.apache.ignite.internal.managers.deployment,org.apache.ignite.
 internal.managers.discovery,org.apache.ignite.internal.managers.encry
 ption,org.apache.ignite.internal.managers.eventstorage,org.apache.ign
 ite.internal.managers.failover,org.apache.ignite.internal.managers.in
 dexing,org.apache.ignite.internal.managers.loadbalancer,org.apache.ig
 nite.internal.managers.systemview,org.apache.ignite.internal.processo
 rs.affinity,org.apache.ignite.internal.processors.authentication,org.
 apache.ignite.internal.processors.cache,org.apache.ignite.internal.pr
 ocessors.cache.distributed.near,org.apache.ignite.internal.processors
 .cache.mvcc,org.apache.ignite.internal.processors.cache.persistence,o
 rg.apache.ignite.internal.processors.cache.persistence.defragmentatio
 n,org.apache.ignite.internal.processors.cache.persistence.filename,or
 g.apache.ignite.internal.processors.cache.version,org.apache.ignite.i
 nternal.processors.cacheobject,org.apache.ignite.internal.processors.
 closure,org.apache.ignite.internal.processors.cluster,org.apache.igni
 te.internal.processors.compress,org.apache.ignite.internal.processors
 .configuration.distributed,org.apache.ignite.internal.processors.cont
 inuous,org.apache.ignite.internal.processors.datastreamer,org.apache.
 ignite.internal.processors.datastructures,org.apache.ignite.internal.
 processors.diagnostic,org.apache.ignite.internal.processors.failure,o
 rg.apache.ignite.internal.processors.job,org.apache.ignite.internal.p
 rocessors.jobmetrics,org.apache.ignite.internal.processors.localtask,
 org.apache.ignite.internal.processors.marshaller,org.apache.ignite.in
 ternal.processors.metastorage,org.apache.ignite.internal.processors.m
 etric,org.apache.ignite.internal.processors.odbc,org.apache.ignite.in
 ternal.processors.performancestatistics,org.apache.ignite.internal.pr
 ocessors.platform,org.apache.ignite.internal.processors.plugin,org.ap
 ache.ignite.internal.processors.pool,org.apache.ignite.internal.proce
 ssors.port,org.apache.ignite.internal.processors.query,org.apache.ign
 ite.internal.processors.resource,org.apache.ignite.internal.processor
 s.rest,org.apache.ignite.internal.processors.schedule,org.apache.igni
 te.internal.processors.security,org.apache.ignite.internal.processors
 .segmentation,org.apache.ignite.internal.processors.service,org.apach
 e.ignite.internal.processors.session,org.apache.ignite.internal.proce
 ssors.subscription,org.apache.ignite.internal.processors.task,org.apa
 che.ignite.internal.processors.timeout,org.apache.ignite.internal.pro
 cessors.tracing,org.apache.ignite.internal.suggestions,org.apache.ign
 ite.internal.util,org.apache.ignite.internal.util.future,org.apache.i
 gnite.internal.util.lang,org.apache.ignite.internal.util.tostring,org
 .apache.ignite.internal.util.typedef,org.apache.ignite.internal.worke
 r,org.apache.ignite.lang,org.apache.ignite.lifecycle,org.apache.ignit
 e.maintenance,org.apache.ignite.marshaller,org.apache.ignite.marshall
 er.jdk,org.apache.ignite.mxbean,org.apache.ignite.plugin,org.apache.i
 gnite.plugin.extensions.communication,org.apache.ignite.scheduler,org
 .apache.ignite.services,org.apache.ignite.spi,org.apache.ignite.spi.d
 iscovery,org.apache.ignite.spi.tracing,org.apache.ignite.thread,org.a
 pache.ignite.transactions";version="2.10.0",org.apache.ignite.interna
 l.binary;uses:="org.apache.ignite,org.apache.ignite.binary,org.apache
 .ignite.configuration,org.apache.ignite.internal,org.apache.ignite.in
 ternal.binary.streams,org.apache.ignite.internal.processors.cache,org
 .apache.ignite.lang,org.apache.ignite.marshaller,org.apache.ignite.pl
 ugin.extensions.communication";version="2.10.0",org.apache.ignite.int
 ernal.binary.builder;uses:="org.apache.ignite.binary,org.apache.ignit
 e.internal.binary";version="2.10.0",org.apache.ignite.internal.binary
 .streams;uses:="org.apache.ignite.internal.binary";version="2.10.0",o
 rg.apache.ignite.internal.client;uses:="org.apache.ignite.cluster,org
 .apache.ignite.internal.client.balancer,org.apache.ignite.internal.cl
 ient.marshaller,org.apache.ignite.internal.client.ssl,org.apache.igni
 te.plugin.security";version="2.10.0",org.apache.ignite.internal.clien
 t.balancer;uses:="org.apache.ignite.internal.client";version="2.10.0"
 ,org.apache.ignite.internal.client.impl;uses:="org.apache.ignite.clus
 ter,org.apache.ignite.internal.client,org.apache.ignite.internal.clie
 nt.balancer,org.apache.ignite.internal.client.impl.connection";versio
 n="2.10.0",org.apache.ignite.internal.client.impl.connection;uses:="j
 avax.net.ssl,org.apache.ignite.cluster,org.apache.ignite.internal.cli
 ent,org.apache.ignite.internal.client.impl,org.apache.ignite.internal
 .processors.rest.client.message,org.apache.ignite.plugin.security";ve
 rsion="2.10.0",org.apache.ignite.internal.client.marshaller;version="
 2.10.0",org.apache.ignite.internal.client.marshaller.jdk;uses:="org.a
 pache.ignite.internal.client.marshaller,org.apache.ignite.lang";versi
 on="2.10.0",org.apache.ignite.internal.client.marshaller.optimized;us
 es:="org.apache.ignite.internal.client.marshaller,org.apache.ignite.i
 nternal.marshaller.optimized,org.apache.ignite.plugin";version="2.10.
 0",org.apache.ignite.internal.client.router;uses:="org.apache.ignite,
 org.apache.ignite.internal.client.ssl,org.apache.ignite.mxbean,org.ap
 ache.ignite.plugin.security";version="2.10.0",org.apache.ignite.inter
 nal.client.router.impl;uses:="org.apache.ignite,org.apache.ignite.fai
 lure,org.apache.ignite.internal.client,org.apache.ignite.internal.cli
 ent.marshaller,org.apache.ignite.internal.client.router,org.apache.ig
 nite.internal.processors.rest.client.message,org.apache.ignite.intern
 al.util.nio,org.apache.ignite.lifecycle";version="2.10.0",org.apache.
 ignite.internal.client.ssl;uses:="javax.net.ssl";version="2.10.0",org
 .apache.ignite.internal.client.thin;uses:="javax.cache.processor,java
 x.net.ssl,org.apache.ignite,org.apache.ignite.binary,org.apache.ignit
 e.cache.query,org.apache.ignite.client,org.apache.ignite.configuratio
 n,org.apache.ignite.internal.binary,org.apache.ignite.internal.binary
 .streams,org.apache.ignite.internal.client.thin.io,org.apache.ignite.
 internal.cust.affinityep.processors.util.typedef,org.apache.ignite.in
 ternal.cust.thin.client.external,org.apache.ignite.internal.processor
 s.affinity";version="2.10.0",org.apache.ignite.internal.client.thin.i
 o;uses:="org.apache.ignite,org.apache.ignite.client";version="2.10.0"
 ,org.apache.ignite.internal.client.thin.io.gridnioserver;uses:="org.a
 pache.ignite,org.apache.ignite.client,org.apache.ignite.configuration
 ,org.apache.ignite.internal.client.thin.io,org.apache.ignite.internal
 .util.nio";version="2.10.0",org.apache.ignite.internal.client.util;us
 es:="org.apache.ignite.internal,org.apache.ignite.internal.client";ve
 rsion="2.10.0",org.apache.ignite.internal.cluster;uses:="org.apache.i
 gnite,org.apache.ignite.cluster,org.apache.ignite.internal,org.apache
 .ignite.internal.processors.cluster.baseline.autoadjust,org.apache.ig
 nite.internal.processors.configuration.distributed,org.apache.ignite.
 internal.processors.subscription,org.apache.ignite.internal.util.futu
 re,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.interna
 l.cluster.graph;uses:="org.apache.ignite.cluster,org.apache.ignite.co
 nfiguration";version="2.10.0",org.apache.ignite.internal.commandline.
 cache.check_indexes_inline_size;uses:="org.apache.ignite,org.apache.i
 gnite.compute,org.apache.ignite.internal.dto,org.apache.ignite.intern
 al.processors.task,org.apache.ignite.internal.visor";version="2.10.0"
 ,org.apache.ignite.internal.commandline.cache.distribution;uses:="org
 .apache.ignite,org.apache.ignite.compute,org.apache.ignite.internal.p
 rocessors.cache.distributed.dht.topology,org.apache.ignite.internal.v
 isor";version="2.10.0",org.apache.ignite.internal.commandline.cache.r
 eset_lost_partitions;uses:="org.apache.ignite.internal.visor";version
 ="2.10.0",org.apache.ignite.internal.commandline.meta.subcommands;use
 s:="org.apache.ignite.internal.dto";version="2.10.0",org.apache.ignit
 e.internal.commandline.meta.tasks;uses:="org.apache.ignite,org.apache
 .ignite.compute,org.apache.ignite.internal,org.apache.ignite.internal
 .binary,org.apache.ignite.internal.dto,org.apache.ignite.internal.pro
 cessors.task,org.apache.ignite.internal.visor";version="2.10.0",org.a
 pache.ignite.internal.commandline.property;uses:="org.apache.ignite.i
 nternal.dto";version="2.10.0",org.apache.ignite.internal.commandline.
 property.tasks;uses:="org.apache.ignite,org.apache.ignite.compute,org
 .apache.ignite.internal.commandline.meta.subcommands,org.apache.ignit
 e.internal.commandline.property,org.apache.ignite.internal.dto,org.ap
 ache.ignite.internal.processors.task,org.apache.ignite.internal.visor
 ";version="2.10.0",org.apache.ignite.internal.compute;uses:="org.apac
 he.ignite";version="2.10.0",org.apache.ignite.internal.cust.affinitye
 p.processors.cache;uses:="org.apache.ignite,org.apache.ignite.cluster
 ,org.apache.ignite.internal,org.apache.ignite.internal.cust.affinitye
 p.processors.cache.dhtatomic.message,org.apache.ignite.internal.proce
 ssors.cache,org.apache.ignite.lang";version="2.10.0",org.apache.ignit
 e.internal.cust.affinityep.processors.cache.client;uses:="javax.cache
 .processor,org.apache.ignite,org.apache.ignite.client,org.apache.igni
 te.internal,org.apache.ignite.internal.cust.affinityep.processors.cac
 he.exception,org.apache.ignite.internal.cust.affinityep.processors.ut
 il.typedef,org.apache.ignite.internal.processors.cache,org.apache.ign
 ite.lang";version="2.10.0",org.apache.ignite.internal.cust.affinityep
 .processors.cache.client.context;uses:="org.apache.ignite.internal.pr
 ocessors.cache";version="2.10.0",org.apache.ignite.internal.cust.affi
 nityep.processors.cache.client.util;uses:="javax.cache.processor,org.
 apache.ignite.internal.cust.affinityep.processors.util.typedef";versi
 on="2.10.0",org.apache.ignite.internal.cust.affinityep.processors.cac
 he.dhtatomic.future;uses:="javax.cache.expiry,javax.cache.processor,o
 rg.apache.ignite,org.apache.ignite.cache,org.apache.ignite.cluster,or
 g.apache.ignite.internal,org.apache.ignite.internal.cust.affinityep.p
 rocessors.cache.dhtatomic.message,org.apache.ignite.internal.cust.aff
 inityep.processors.cache.dhtatomic.result,org.apache.ignite.internal.
 cust.affinityep.processors.cache.server,org.apache.ignite.internal.pr
 ocessors.affinity,org.apache.ignite.internal.processors.cache,org.apa
 che.ignite.internal.processors.cache.distributed.dht,org.apache.ignit
 e.internal.processors.cache.dr,org.apache.ignite.internal.processors.
 cache.version,org.apache.ignite.internal.util.tostring,org.apache.ign
 ite.lang";version="2.10.0",org.apache.ignite.internal.cust.affinityep
 .processors.cache.dhtatomic.message;uses:="javax.cache.processor,org.
 apache.ignite,org.apache.ignite.cache,org.apache.ignite.internal,org.
 apache.ignite.internal.cust.affinityep.processors.cache.dhtatomic.res
 ult,org.apache.ignite.internal.processors.affinity,org.apache.ignite.
 internal.processors.cache,org.apache.ignite.internal.processors.cache
 .version,org.apache.ignite.internal.processors.timeout,org.apache.ign
 ite.internal.util,org.apache.ignite.internal.util.tostring,org.apache
 .ignite.plugin.extensions.communication";version="2.10.0",org.apache.
 ignite.internal.cust.affinityep.processors.cache.dhtatomic.result;use
 s:="org.apache.ignite,org.apache.ignite.internal.cust.affinityep.proc
 essors.cache.dhtatomic.future,org.apache.ignite.internal.processors.c
 ache,org.apache.ignite.internal.processors.cache.distributed.dht,org.
 apache.ignite.internal.processors.cache.version,org.apache.ignite.lan
 g,org.apache.ignite.plugin.extensions.communication";version="2.10.0"
 ,org.apache.ignite.internal.cust.affinityep.processors.cache.dhtatomi
 c.result.triple;uses:="org.apache.ignite.internal.processors.affinity
 ,org.apache.ignite.internal.processors.cache,org.apache.ignite.intern
 al.processors.cache.distributed.dht,org.apache.ignite.internal.proces
 sors.cache.distributed.dht.topology,org.apache.ignite.internal.proces
 sors.cache.persistence,org.apache.ignite.internal.processors.cache.pe
 rsistence.tree,org.apache.ignite.internal.processors.cache.tree,org.a
 pache.ignite.internal.processors.cache.version,org.apache.ignite.inte
 rnal.util";version="2.10.0",org.apache.ignite.internal.cust.affinitye
 p.processors.cache.exception;version="2.10.0",org.apache.ignite.inter
 nal.cust.affinityep.processors.cache.server;uses:="org.apache.ignite,
 org.apache.ignite.internal,org.apache.ignite.internal.cust.affinityep
 .processors.cache.dhtatomic.message,org.apache.ignite.internal.cust.a
 ffinityep.processors.cache.server.context,org.apache.ignite.internal.
 processors.affinity,org.apache.ignite.internal.processors.cache,org.a
 pache.ignite.internal.util.typedef";version="2.10.0",org.apache.ignit
 e.internal.cust.affinityep.processors.cache.server.context;uses:="org
 .apache.ignite.internal.processors.cache";version="2.10.0",org.apache
 .ignite.internal.cust.affinityep.processors.metrics;uses:="org.apache
 .ignite.internal.cust.server.base.metrics,org.apache.ignite.mxbean";v
 ersion="2.10.0",org.apache.ignite.internal.cust.affinityep.processors
 .metrics.impl;uses:="org.apache.ignite.internal.cust.affinityep.proce
 ssors.metrics";version="2.10.0",org.apache.ignite.internal.cust.affin
 ityep.processors.util;uses:="org.apache.ignite,org.apache.ignite.clie
 nt,org.apache.ignite.internal.cust.affinityep.processors.util.typedef
 ,org.apache.ignite.internal.processors.affinity,org.apache.ignite.int
 ernal.processors.cache,org.apache.ignite.internal.processors.cache.di
 stributed.dht,org.apache.ignite.internal.processors.cache.distributed
 .dht.atomic";version="2.10.0",org.apache.ignite.internal.cust.affinit
 yep.processors.util.typedef;uses:="org.apache.ignite.client";version=
 "2.10.0",org.apache.ignite.internal.cust.common.node;uses:="org.apach
 e.ignite.cluster,org.apache.ignite.internal.dto";version="2.10.0",org
 .apache.ignite.internal.cust.common.predicate;uses:="org.apache.ignit
 e.cluster,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.
 internal.cust.demo.cachestore;uses:="javax.cache,javax.cache.integrat
 ion,org.apache.ignite,org.apache.ignite.cache.store,org.apache.ignite
 .internal.cust.demo.entity,org.apache.ignite.resources";version="2.10
 .0",org.apache.ignite.internal.cust.demo.entity;uses:="javax.cache,ja
 vax.cache.integration,org.apache.ignite,org.apache.ignite.cache.store
 ,org.apache.ignite.resources";version="2.10.0",org.apache.ignite.inte
 rnal.cust.demo.ep;uses:="javax.cache.processor,org.apache.ignite,org.
 apache.ignite.client";version="2.10.0",org.apache.ignite.internal.cus
 t.demo.ep.affinity;uses:="javax.cache.processor,org.apache.ignite.cli
 ent,org.apache.ignite.internal.cust.demo.entity";version="2.10.0",org
 .apache.ignite.internal.cust.failure;uses:="org.apache.ignite,org.apa
 che.ignite.failure";version="2.10.0",org.apache.ignite.internal.cust.
 server;uses:="org.apache.ignite,org.apache.ignite.internal,org.apache
 .ignite.internal.cust.server.cache,org.apache.ignite.internal.cust.se
 rver.keyanalysis,org.apache.ignite.internal.cust.server.modulecheck,o
 rg.apache.ignite.internal.cust.server.node,org.apache.ignite.internal
 .cust.server.performance,org.apache.ignite.internal.cust.server.simpl
 ehelper,org.apache.ignite.internal.cust.server.stript,org.apache.igni
 te.internal.managers,org.apache.ignite.spi";version="2.10.0",org.apac
 he.ignite.internal.cust.server.base;uses:="org.apache.ignite,org.apac
 he.ignite.internal,org.apache.ignite.internal.util.tostring";version=
 "2.10.0",org.apache.ignite.internal.cust.server.base.content;version=
 "2.10.0",org.apache.ignite.internal.cust.server.base.metrics;uses:="j
 avax.management,org.apache.ignite,org.apache.ignite.mxbean";version="
 2.10.0",org.apache.ignite.internal.cust.server.cache;uses:="org.apach
 e.ignite.cache,org.apache.ignite.internal.cust.server,org.apache.igni
 te.internal.cust.server.base";version="2.10.0",org.apache.ignite.inte
 rnal.cust.server.keyanalysis;uses:="org.apache.ignite.internal.cust.s
 erver,org.apache.ignite.internal.cust.server.base,org.apache.ignite.i
 nternal.cust.server.keyanalysis.bigkey,org.apache.ignite.internal.cus
 t.server.keyanalysis.bigkey.config,org.apache.ignite.internal.cust.se
 rver.keyanalysis.hotkey,org.apache.ignite.internal.cust.server.keyana
 lysis.hotkey.config,org.apache.ignite.internal.cust.server.keyanalysi
 s.slowkey,org.apache.ignite.internal.cust.server.keyanalysis.slowkey.
 config";version="2.10.0",org.apache.ignite.internal.cust.server.keyan
 alysis.bigkey;uses:="org.apache.ignite,org.apache.ignite.internal,org
 .apache.ignite.internal.cust.server.base,org.apache.ignite.internal.c
 ust.server.keyanalysis,org.apache.ignite.internal.cust.server.keyanal
 ysis.bigkey.config,org.apache.ignite.internal.cust.server.keyanalysis
 .bigkey.mr.change,org.apache.ignite.internal.cust.server.keyanalysis.
 bigkey.mr.clean,org.apache.ignite.internal.cust.server.keyanalysis.bi
 gkey.mr.data,org.apache.ignite.internal.cust.server.keyanalysis.bigke
 y.mr.state,org.apache.ignite.internal.processors.cache";version="2.10
 .0",org.apache.ignite.internal.cust.server.keyanalysis.bigkey.config;
 uses:="org.apache.ignite.internal.cust.server.keyanalysis.bigkey";ver
 sion="2.10.0",org.apache.ignite.internal.cust.server.keyanalysis.bigk
 ey.metrics;uses:="org.apache.ignite.internal.cust.server.base.metrics
 ,org.apache.ignite.mxbean";version="2.10.0",org.apache.ignite.interna
 l.cust.server.keyanalysis.bigkey.metrics.impl;uses:="org.apache.ignit
 e.internal.cust.server.keyanalysis.bigkey,org.apache.ignite.internal.
 cust.server.keyanalysis.bigkey.metrics";version="2.10.0",org.apache.i
 gnite.internal.cust.server.keyanalysis.bigkey.mr.change;uses:="org.ap
 ache.ignite,org.apache.ignite.cluster,org.apache.ignite.compute,org.a
 pache.ignite.internal,org.apache.ignite.internal.cust.server.keyanaly
 sis.bigkey,org.apache.ignite.internal.dto,org.apache.ignite.resources
 ";version="2.10.0",org.apache.ignite.internal.cust.server.keyanalysis
 .bigkey.mr.clean;uses:="org.apache.ignite,org.apache.ignite.cluster,o
 rg.apache.ignite.compute,org.apache.ignite.internal,org.apache.ignite
 .internal.dto,org.apache.ignite.resources";version="2.10.0",org.apach
 e.ignite.internal.cust.server.keyanalysis.bigkey.mr.data;uses:="org.a
 pache.ignite,org.apache.ignite.cluster,org.apache.ignite.compute,org.
 apache.ignite.internal,org.apache.ignite.internal.cust.common.node,or
 g.apache.ignite.internal.cust.server.keyanalysis.bigkey,org.apache.ig
 nite.internal.dto,org.apache.ignite.resources";version="2.10.0",org.a
 pache.ignite.internal.cust.server.keyanalysis.bigkey.mr.state;uses:="
 org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.compute
 ,org.apache.ignite.internal,org.apache.ignite.internal.cust.common.no
 de,org.apache.ignite.internal.cust.server.keyanalysis.bigkey,org.apac
 he.ignite.internal.dto,org.apache.ignite.resources";version="2.10.0",
 org.apache.ignite.internal.cust.server.keyanalysis.bigkey.util;versio
 n="2.10.0",org.apache.ignite.internal.cust.server.keyanalysis.hotkey;
 uses:="org.apache.ignite,org.apache.ignite.internal,org.apache.ignite
 .internal.cust.affinityep.processors.util.typedef,org.apache.ignite.i
 nternal.cust.server.base,org.apache.ignite.internal.cust.server.keyan
 alysis,org.apache.ignite.internal.cust.server.keyanalysis.hotkey.conf
 ig,org.apache.ignite.internal.cust.server.keyanalysis.hotkey.mr.chang
 e,org.apache.ignite.internal.cust.server.keyanalysis.hotkey.mr.clean,
 org.apache.ignite.internal.cust.server.keyanalysis.hotkey.mr.data,org
 .apache.ignite.internal.cust.server.keyanalysis.hotkey.mr.state,org.a
 pache.ignite.internal.cust.server.keyanalysis.ratelimit,org.apache.ig
 nite.internal.processors.cache";version="2.10.0",org.apache.ignite.in
 ternal.cust.server.keyanalysis.hotkey.config;version="2.10.0",org.apa
 che.ignite.internal.cust.server.keyanalysis.hotkey.metrics;uses:="org
 .apache.ignite.internal.cust.server.base.metrics,org.apache.ignite.mx
 bean";version="2.10.0",org.apache.ignite.internal.cust.server.keyanal
 ysis.hotkey.metrics.impl;uses:="org.apache.ignite.internal.cust.serve
 r.keyanalysis.hotkey,org.apache.ignite.internal.cust.server.keyanalys
 is.hotkey.metrics";version="2.10.0",org.apache.ignite.internal.cust.s
 erver.keyanalysis.hotkey.mr.change;uses:="org.apache.ignite,org.apach
 e.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.internal
 ,org.apache.ignite.internal.dto,org.apache.ignite.resources";version=
 "2.10.0",org.apache.ignite.internal.cust.server.keyanalysis.hotkey.mr
 .clean;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache.
 ignite.compute,org.apache.ignite.internal,org.apache.ignite.internal.
 dto,org.apache.ignite.resources";version="2.10.0",org.apache.ignite.i
 nternal.cust.server.keyanalysis.hotkey.mr.data;uses:="org.apache.igni
 te,org.apache.ignite.cluster,org.apache.ignite.compute,org.apache.ign
 ite.internal,org.apache.ignite.internal.cust.common.node,org.apache.i
 gnite.internal.dto,org.apache.ignite.resources";version="2.10.0",org.
 apache.ignite.internal.cust.server.keyanalysis.hotkey.mr.state;uses:=
 "org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.comput
 e,org.apache.ignite.internal,org.apache.ignite.internal.cust.common.n
 ode,org.apache.ignite.internal.dto,org.apache.ignite.resources";versi
 on="2.10.0",org.apache.ignite.internal.cust.server.keyanalysis.rateli
 mit;version="2.10.0",org.apache.ignite.internal.cust.server.keyanalys
 is.slowkey;uses:="org.apache.ignite,org.apache.ignite.internal,org.ap
 ache.ignite.internal.cust.affinityep.processors.util.typedef,org.apac
 he.ignite.internal.cust.server.base,org.apache.ignite.internal.cust.s
 erver.keyanalysis,org.apache.ignite.internal.cust.server.keyanalysis.
 slowkey.config,org.apache.ignite.internal.cust.server.keyanalysis.slo
 wkey.mr.change,org.apache.ignite.internal.cust.server.keyanalysis.slo
 wkey.mr.clean,org.apache.ignite.internal.cust.server.keyanalysis.slow
 key.mr.data,org.apache.ignite.internal.cust.server.keyanalysis.slowke
 y.mr.state,org.apache.ignite.internal.processors.cache";version="2.10
 .0",org.apache.ignite.internal.cust.server.keyanalysis.slowkey.config
 ;uses:="org.apache.ignite.internal.cust.server.keyanalysis.slowkey";v
 ersion="2.10.0",org.apache.ignite.internal.cust.server.keyanalysis.sl
 owkey.metrics;uses:="org.apache.ignite.internal.cust.server.base.metr
 ics,org.apache.ignite.mxbean";version="2.10.0",org.apache.ignite.inte
 rnal.cust.server.keyanalysis.slowkey.metrics.impl;uses:="org.apache.i
 gnite.internal.cust.server.keyanalysis.slowkey,org.apache.ignite.inte
 rnal.cust.server.keyanalysis.slowkey.metrics";version="2.10.0",org.ap
 ache.ignite.internal.cust.server.keyanalysis.slowkey.mr.change;uses:=
 "org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.comput
 e,org.apache.ignite.internal,org.apache.ignite.internal.cust.server.k
 eyanalysis.slowkey,org.apache.ignite.internal.dto,org.apache.ignite.r
 esources";version="2.10.0",org.apache.ignite.internal.cust.server.key
 analysis.slowkey.mr.clean;uses:="org.apache.ignite,org.apache.ignite.
 cluster,org.apache.ignite.compute,org.apache.ignite.internal,org.apac
 he.ignite.internal.dto,org.apache.ignite.resources";version="2.10.0",
 org.apache.ignite.internal.cust.server.keyanalysis.slowkey.mr.data;us
 es:="org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.co
 mpute,org.apache.ignite.internal,org.apache.ignite.internal.cust.comm
 on.node,org.apache.ignite.internal.cust.server.keyanalysis.slowkey,or
 g.apache.ignite.internal.dto,org.apache.ignite.resources";version="2.
 10.0",org.apache.ignite.internal.cust.server.keyanalysis.slowkey.mr.s
 tate;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache.ig
 nite.compute,org.apache.ignite.internal,org.apache.ignite.internal.cu
 st.common.node,org.apache.ignite.internal.cust.server.keyanalysis.slo
 wkey,org.apache.ignite.internal.dto,org.apache.ignite.resources";vers
 ion="2.10.0",org.apache.ignite.internal.cust.server.keyanalysis.slowk
 ey.util;version="2.10.0",org.apache.ignite.internal.cust.server.modul
 echeck;uses:="org.apache.ignite.internal,org.apache.ignite.internal.c
 ust.server,org.apache.ignite.internal.cust.server.base,org.apache.ign
 ite.internal.cust.server.modulecheck.entity,org.apache.ignite.interna
 l.cust.server.modulecheck.mr.state";version="2.10.0",org.apache.ignit
 e.internal.cust.server.modulecheck.entity;uses:="javax.management,org
 .apache.ignite.internal";version="2.10.0",org.apache.ignite.internal.
 cust.server.modulecheck.metric;uses:="org.apache.ignite.internal.cust
 .server.base.metrics,org.apache.ignite.internal.cust.server.moduleche
 ck.entity,org.apache.ignite.mxbean";version="2.10.0",org.apache.ignit
 e.internal.cust.server.modulecheck.mr.partition.bad;uses:="org.apache
 .ignite,org.apache.ignite.cluster,org.apache.ignite.compute,org.apach
 e.ignite.internal,org.apache.ignite.internal.dto,org.apache.ignite.re
 sources";version="2.10.0",org.apache.ignite.internal.cust.server.modu
 lecheck.mr.partition.good;uses:="org.apache.ignite,org.apache.ignite.
 cluster,org.apache.ignite.compute,org.apache.ignite.internal,org.apac
 he.ignite.internal.dto,org.apache.ignite.resources";version="2.10.0",
 org.apache.ignite.internal.cust.server.modulecheck.mr.state;uses:="or
 g.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.compute,o
 rg.apache.ignite.internal,org.apache.ignite.internal.cust.common.node
 ,org.apache.ignite.internal.cust.server.modulecheck.entity,org.apache
 .ignite.internal.dto,org.apache.ignite.resources";version="2.10.0",or
 g.apache.ignite.internal.cust.server.node;uses:="org.apache.ignite.in
 ternal.cust.server,org.apache.ignite.internal.cust.server.base,org.ap
 ache.ignite.internal.cust.server.node.link,org.apache.ignite.internal
 .cust.server.node.link.entity,org.apache.ignite.internal.cust.server.
 node.storage.entity";version="2.10.0",org.apache.ignite.internal.cust
 .server.node.link;uses:="org.apache.ignite,org.apache.ignite.configur
 ation,org.apache.ignite.internal,org.apache.ignite.internal.cust.serv
 er.base,org.apache.ignite.internal.cust.server.node.link.check,org.ap
 ache.ignite.internal.cust.server.node.link.entity,org.apache.ignite.i
 nternal.cust.server.node.link.mr.messageip.change,org.apache.ignite.i
 nternal.util.nio";version="2.10.0",org.apache.ignite.internal.cust.se
 rver.node.link.check;uses:="org.apache.ignite,org.apache.ignite.confi
 guration,org.apache.ignite.internal.cust.server.node.link,org.apache.
 ignite.internal.util,org.apache.ignite.internal.util.nio,org.apache.i
 gnite.lang";version="2.10.0",org.apache.ignite.internal.cust.server.n
 ode.link.entity;uses:="org.apache.ignite.internal,org.apache.ignite.i
 nternal.dto,org.apache.ignite.internal.util.nio";version="2.10.0",org
 .apache.ignite.internal.cust.server.node.link.metrics;uses:="org.apac
 he.ignite.internal.cust.server.base.metrics,org.apache.ignite.interna
 l.util.nio,org.apache.ignite.mxbean";version="2.10.0",org.apache.igni
 te.internal.cust.server.node.link.mr.linkdetail;uses:="org.apache.ign
 ite,org.apache.ignite.cluster,org.apache.ignite.compute,org.apache.ig
 nite.internal,org.apache.ignite.internal.cust.common.node,org.apache.
 ignite.internal.cust.server.node.link.entity,org.apache.ignite.intern
 al.dto,org.apache.ignite.resources";version="2.10.0",org.apache.ignit
 e.internal.cust.server.node.link.mr.messageip.change;uses:="org.apach
 e.ignite,org.apache.ignite.cluster,org.apache.ignite.compute,org.apac
 he.ignite.internal,org.apache.ignite.internal.dto,org.apache.ignite.r
 esources";version="2.10.0",org.apache.ignite.internal.cust.server.nod
 e.link.mr.messageip.data;uses:="org.apache.ignite,org.apache.ignite.c
 luster,org.apache.ignite.compute,org.apache.ignite.internal,org.apach
 e.ignite.internal.dto,org.apache.ignite.resources";version="2.10.0",o
 rg.apache.ignite.internal.cust.server.node.link.util;uses:="org.apach
 e.ignite.internal.cust.server.node.link.entity,org.apache.ignite.inte
 rnal.util.nio";version="2.10.0",org.apache.ignite.internal.cust.serve
 r.node.storage.entity;uses:="org.apache.ignite.configuration,org.apac
 he.ignite.internal,org.apache.ignite.internal.dto";version="2.10.0",o
 rg.apache.ignite.internal.cust.server.node.storage.mr.storageInfo;use
 s:="org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.com
 pute,org.apache.ignite.internal,org.apache.ignite.internal.cust.commo
 n.node,org.apache.ignite.internal.cust.server.node.storage.entity,org
 .apache.ignite.internal.dto,org.apache.ignite.resources";version="2.1
 0.0",org.apache.ignite.internal.cust.server.performance;uses:="org.ap
 ache.ignite.internal.cust.server,org.apache.ignite.internal.cust.serv
 er.base,org.apache.ignite.internal.cust.server.performance.ep";versio
 n="2.10.0",org.apache.ignite.internal.cust.server.performance.ep;uses
 :="javax.cache.processor,org.apache.ignite,org.apache.ignite.cluster,
 org.apache.ignite.internal,org.apache.ignite.internal.cust.server.bas
 e,org.apache.ignite.internal.cust.server.performance.ep.mr.deploystat
 e,org.apache.ignite.internal.cust.server.performance.ep.mr.performanc
 e,org.apache.ignite.internal.managers.deployment";version="2.10.0",or
 g.apache.ignite.internal.cust.server.performance.ep.entity;uses:="jav
 ax.management,org.apache.ignite.internal.cust.common.node,org.apache.
 ignite.internal.cust.server.performance.ep.metric";version="2.10.0",o
 rg.apache.ignite.internal.cust.server.performance.ep.metric;uses:="or
 g.apache.ignite.internal.cust.server.base.metrics,org.apache.ignite.m
 xbean";version="2.10.0",org.apache.ignite.internal.cust.server.perfor
 mance.ep.metric.impl;uses:="org.apache.ignite.internal.cust.server.pe
 rformance.ep.metric";version="2.10.0",org.apache.ignite.internal.cust
 .server.performance.ep.mr.deploystate;uses:="org.apache.ignite,org.ap
 ache.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.inter
 nal,org.apache.ignite.internal.cust.common.node,org.apache.ignite.int
 ernal.dto,org.apache.ignite.resources";version="2.10.0",org.apache.ig
 nite.internal.cust.server.performance.ep.mr.performance;uses:="org.ap
 ache.ignite,org.apache.ignite.cluster,org.apache.ignite.compute,org.a
 pache.ignite.internal,org.apache.ignite.internal.cust.common.node,org
 .apache.ignite.internal.dto,org.apache.ignite.resources";version="2.1
 0.0",org.apache.ignite.internal.cust.server.performance.ep.mr.undeplo
 y;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache.ignit
 e.compute,org.apache.ignite.internal,org.apache.ignite.internal.dto,o
 rg.apache.ignite.resources";version="2.10.0",org.apache.ignite.intern
 al.cust.server.performance.ep.mr.undeployall;uses:="org.apache.ignite
 ,org.apache.ignite.cluster,org.apache.ignite.compute,org.apache.ignit
 e.internal,org.apache.ignite.internal.dto,org.apache.ignite.resources
 ";version="2.10.0",org.apache.ignite.internal.cust.server.simplehelpe
 r;uses:="org.apache.ignite.internal.cust.server,org.apache.ignite.int
 ernal.cust.server.base,org.apache.ignite.internal.processors.cache";v
 ersion="2.10.0",org.apache.ignite.internal.cust.server.stript;uses:="
 org.apache.ignite.internal.cust.server,org.apache.ignite.internal.cus
 t.server.base,org.apache.ignite.internal.cust.server.stript.mr.queuei
 nfo";version="2.10.0",org.apache.ignite.internal.cust.server.stript.e
 ntity;version="2.10.0",org.apache.ignite.internal.cust.server.stript.
 mr.queueinfo;uses:="org.apache.ignite,org.apache.ignite.cluster,org.a
 pache.ignite.compute,org.apache.ignite.internal,org.apache.ignite.int
 ernal.cust.common.node,org.apache.ignite.internal.cust.server.stript.
 entity,org.apache.ignite.internal.dto,org.apache.ignite.resources";ve
 rsion="2.10.0",org.apache.ignite.internal.cust.server.stript.mr.queue
 size;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache.ig
 nite.compute,org.apache.ignite.internal,org.apache.ignite.internal.dt
 o,org.apache.ignite.resources";version="2.10.0",org.apache.ignite.int
 ernal.cust.thin.base;version="2.10.0",org.apache.ignite.internal.cust
 .thin.client.external;uses:="org.apache.ignite.client,org.apache.igni
 te.internal.client.thin";version="2.10.0",org.apache.ignite.internal.
 cust.thin.client.internal;uses:="org.apache.ignite.internal.binary,or
 g.apache.ignite.internal.processors.platform.client";version="2.10.0"
 ,org.apache.ignite.internal.cust.thin.metrics;uses:="javax.management
 ,org.apache.ignite.internal.cust.server.base.metrics,org.apache.ignit
 e.internal.cust.thin.metrics.type";version="2.10.0",org.apache.ignite
 .internal.cust.thin.metrics.type;uses:="org.apache.ignite.client,org.
 apache.ignite.internal.cust.server.base.metrics,org.apache.ignite.int
 ernal.cust.thin.base,org.apache.ignite.internal.cust.thin.metrics,org
 .apache.ignite.internal.cust.thin.metrics.type.cacheop.entity,org.apa
 che.ignite.mxbean";version="2.10.0",org.apache.ignite.internal.cust.t
 hin.metrics.type.cacheep.entity;version="2.10.0",org.apache.ignite.in
 ternal.cust.thin.metrics.type.cacheep.ep;uses:="org.apache.ignite.int
 ernal.cust.thin.metrics.type,org.apache.ignite.mxbean";version="2.10.
 0",org.apache.ignite.internal.cust.thin.metrics.type.cacheop.entity;v
 ersion="2.10.0",org.apache.ignite.internal.cust.thin.metrics.type.cac
 heop.op;uses:="org.apache.ignite.internal.cust.thin.metrics.type,org.
 apache.ignite.internal.cust.thin.metrics.type.cacheop.entity,org.apac
 he.ignite.mxbean";version="2.10.0",org.apache.ignite.internal.cust.th
 in.metrics.type.single;uses:="org.apache.ignite.internal.cust.thin.me
 trics,org.apache.ignite.internal.cust.thin.metrics.type,org.apache.ig
 nite.mxbean";version="2.10.0",org.apache.ignite.internal.cust.thin.me
 trics.type.single.affinityep;uses:="org.apache.ignite.internal.cust.t
 hin.metrics.type,org.apache.ignite.mxbean";version="2.10.0",org.apach
 e.ignite.internal.cust.thin.metrics.type.single.tx;uses:="org.apache.
 ignite.internal.cust.thin.metrics.type,org.apache.ignite.mxbean";vers
 ion="2.10.0",org.apache.ignite.internal.direct;uses:="org.apache.igni
 te.internal.processors.affinity,org.apache.ignite.lang,org.apache.ign
 ite.plugin.extensions.communication";version="2.10.0",org.apache.igni
 te.internal.direct.state;uses:="org.apache.ignite.lang";version="2.10
 .0",org.apache.ignite.internal.direct.stream;uses:="org.apache.ignite
 .internal.processors.affinity,org.apache.ignite.lang,org.apache.ignit
 e.plugin.extensions.communication";version="2.10.0",org.apache.ignite
 .internal.direct.stream.v1;uses:="org.apache.ignite.internal.direct.s
 tream,org.apache.ignite.internal.processors.affinity,org.apache.ignit
 e.lang,org.apache.ignite.plugin.extensions.communication";version="2.
 10.0",org.apache.ignite.internal.direct.stream.v2;uses:="org.apache.i
 gnite.internal.direct.stream,org.apache.ignite.internal.processors.af
 finity,org.apache.ignite.lang,org.apache.ignite.plugin.extensions.com
 munication";version="2.10.0",org.apache.ignite.internal.direct.stream
 .v3;uses:="org.apache.ignite.internal.direct.stream.v2,org.apache.ign
 ite.internal.processors.affinity,org.apache.ignite.plugin.extensions.
 communication";version="2.10.0",org.apache.ignite.internal.dto;versio
 n="2.10.0",org.apache.ignite.internal.events;uses:="org.apache.ignite
 .events,org.apache.ignite.internal.managers.discovery,org.apache.igni
 te.internal.processors.affinity";version="2.10.0",org.apache.ignite.i
 nternal.executor;uses:="org.apache.ignite.internal,org.apache.ignite.
 internal.cluster";version="2.10.0",org.apache.ignite.internal.jdbc;us
 es:="org.apache.ignite,org.apache.ignite.compute";version="2.10.0",or
 g.apache.ignite.internal.jdbc.thin;uses:="javax.net.ssl,org.apache.ig
 nite.binary,org.apache.ignite.internal.binary,org.apache.ignite.inter
 nal.processors.affinity,org.apache.ignite.internal.processors.odbc.jd
 bc,org.apache.ignite.internal.sql.optimizer.affinity,org.apache.ignit
 e.internal.util";version="2.10.0",org.apache.ignite.internal.jdbc2;us
 es:="org.apache.ignite.cache.query,org.apache.ignite.internal.process
 ors.odbc.jdbc";version="2.10.0",org.apache.ignite.internal.logger.pla
 tform;uses:="org.apache.ignite,org.apache.ignite.internal.processors.
 platform,org.apache.ignite.internal.processors.platform.callback";ver
 sion="2.10.0",org.apache.ignite.internal.maintenance;uses:="org.apach
 e.ignite,org.apache.ignite.internal,org.apache.ignite.internal.proces
 sors,org.apache.ignite.internal.processors.cache.persistence.file,org
 .apache.ignite.internal.processors.cache.persistence.filename,org.apa
 che.ignite.maintenance";version="2.10.0",org.apache.ignite.internal.m
 anagers;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache
 .ignite.internal,org.apache.ignite.internal.util,org.apache.ignite.in
 ternal.util.tostring,org.apache.ignite.internal.worker,org.apache.ign
 ite.lang,org.apache.ignite.spi,org.apache.ignite.spi.discovery,org.ap
 ache.ignite.thread";version="2.10.0",org.apache.ignite.internal.manag
 ers.checkpoint;uses:="org.apache.ignite,org.apache.ignite.compute,org
 .apache.ignite.internal,org.apache.ignite.internal.managers,org.apach
 e.ignite.lang,org.apache.ignite.plugin.extensions.communication,org.a
 pache.ignite.spi.checkpoint";version="2.10.0",org.apache.ignite.inter
 nal.managers.collision;uses:="org.apache.ignite,org.apache.ignite.com
 pute,org.apache.ignite.internal,org.apache.ignite.internal.managers,o
 rg.apache.ignite.internal.processors.job,org.apache.ignite.spi.collis
 ion";version="2.10.0",org.apache.ignite.internal.managers.communicati
 on;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache.igni
 te.configuration,org.apache.ignite.internal,org.apache.ignite.interna
 l.managers,org.apache.ignite.internal.managers.deployment,org.apache.
 ignite.internal.processors.tracing,org.apache.ignite.internal.process
 ors.tracing.messages,org.apache.ignite.lang,org.apache.ignite.plugin.
 extensions.communication,org.apache.ignite.spi.communication";version
 ="2.10.0",org.apache.ignite.internal.managers.deployment;uses:="org.a
 pache.ignite,org.apache.ignite.cluster,org.apache.ignite.compute,org.
 apache.ignite.configuration,org.apache.ignite.internal,org.apache.ign
 ite.internal.managers,org.apache.ignite.internal.util.lang,org.apache
 .ignite.lang,org.apache.ignite.plugin.extensions.communication,org.ap
 ache.ignite.spi.deployment";version="2.10.0",org.apache.ignite.intern
 al.managers.deployment.protocol.gg;uses:="org.apache.ignite.internal.
 managers.deployment";version="2.10.0",org.apache.ignite.internal.mana
 gers.discovery;uses:="org.apache.ignite,org.apache.ignite.cache,org.a
 pache.ignite.cluster,org.apache.ignite.configuration,org.apache.ignit
 e.events,org.apache.ignite.internal,org.apache.ignite.internal.manage
 rs,org.apache.ignite.internal.processors.affinity,org.apache.ignite.i
 nternal.processors.cache,org.apache.ignite.internal.processors.cluste
 r,org.apache.ignite.internal.processors.jobmetrics,org.apache.ignite.
 internal.util.future,org.apache.ignite.lang,org.apache.ignite.spi.dis
 covery";version="2.10.0",org.apache.ignite.internal.managers.encrypti
 on;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache.igni
 te.internal,org.apache.ignite.internal.managers,org.apache.ignite.int
 ernal.pagemem.wal.record,org.apache.ignite.internal.processors.cache,
 org.apache.ignite.internal.processors.cache.persistence.checkpoint,or
 g.apache.ignite.internal.processors.cache.persistence.metastorage,org
 .apache.ignite.internal.processors.cluster,org.apache.ignite.internal
 .util.future,org.apache.ignite.internal.util.typedef,org.apache.ignit
 e.lang,org.apache.ignite.mxbean,org.apache.ignite.plugin.extensions.c
 ommunication,org.apache.ignite.spi,org.apache.ignite.spi.discovery,or
 g.apache.ignite.spi.encryption";version="2.10.0",org.apache.ignite.in
 ternal.managers.eventstorage;uses:="org.apache.ignite,org.apache.igni
 te.cluster,org.apache.ignite.events,org.apache.ignite.internal,org.ap
 ache.ignite.internal.managers,org.apache.ignite.internal.managers.dis
 covery,org.apache.ignite.lang,org.apache.ignite.plugin.extensions.com
 munication,org.apache.ignite.spi.eventstorage";version="2.10.0",org.a
 pache.ignite.internal.managers.failover;uses:="org.apache.ignite,org.
 apache.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.int
 ernal,org.apache.ignite.internal.managers,org.apache.ignite.internal.
 managers.loadbalancer,org.apache.ignite.internal.processors.affinity,
 org.apache.ignite.spi.failover";version="2.10.0",org.apache.ignite.in
 ternal.managers.indexing;uses:="org.apache.ignite,org.apache.ignite.i
 nternal,org.apache.ignite.internal.managers,org.apache.ignite.spi,org
 .apache.ignite.spi.indexing";version="2.10.0",org.apache.ignite.inter
 nal.managers.loadbalancer;uses:="org.apache.ignite,org.apache.ignite.
 cluster,org.apache.ignite.compute,org.apache.ignite.internal,org.apac
 he.ignite.internal.managers,org.apache.ignite.spi.loadbalancing";vers
 ion="2.10.0",org.apache.ignite.internal.managers.systemview;uses:="ja
 vax.management,org.apache.ignite,org.apache.ignite.internal,org.apach
 e.ignite.internal.managers,org.apache.ignite.internal.processors.cach
 e,org.apache.ignite.internal.util,org.apache.ignite.spi,org.apache.ig
 nite.spi.metric.jmx,org.apache.ignite.spi.systemview,org.apache.ignit
 e.spi.systemview.view";version="2.10.0",org.apache.ignite.internal.ma
 nagers.systemview.walker;uses:="org.apache.ignite.spi.systemview.view
 ";version="2.10.0",org.apache.ignite.internal.managers.tracing;uses:=
 "org.apache.ignite,org.apache.ignite.internal,org.apache.ignite.inter
 nal.managers,org.apache.ignite.internal.processors.tracing,org.apache
 .ignite.internal.processors.tracing.messages,org.apache.ignite.spi.tr
 acing";version="2.10.0",org.apache.ignite.internal.marshaller.optimiz
 ed;uses:="org.apache.ignite,org.apache.ignite.internal.util.io,org.ap
 ache.ignite.marshaller";version="2.10.0",org.apache.ignite.internal.m
 em;uses:="org.apache.ignite";version="2.10.0",org.apache.ignite.inter
 nal.mem.file;uses:="org.apache.ignite,org.apache.ignite.internal.mem"
 ;version="2.10.0",org.apache.ignite.internal.mem.unsafe;uses:="org.ap
 ache.ignite,org.apache.ignite.internal.mem";version="2.10.0",org.apac
 he.ignite.internal.metric;uses:="org.apache.ignite.internal.processor
 s.metric";version="2.10.0",org.apache.ignite.internal.mxbean;uses:="j
 avax.management";version="2.10.0",org.apache.ignite.internal.pagemem;
 uses:="org.apache.ignite,org.apache.ignite.internal.metric";version="
 2.10.0",org.apache.ignite.internal.pagemem.impl;uses:="org.apache.ign
 ite,org.apache.ignite.configuration,org.apache.ignite.internal.mem,or
 g.apache.ignite.internal.metric,org.apache.ignite.internal.pagemem,or
 g.apache.ignite.internal.processors.cache,org.apache.ignite.internal.
 processors.metric.impl";version="2.10.0",org.apache.ignite.internal.p
 agemem.store;uses:="org.apache.ignite,org.apache.ignite.configuration
 ,org.apache.ignite.internal.processors.cache,org.apache.ignite.intern
 al.processors.cache.persistence,org.apache.ignite.internal.processors
 .cache.persistence.pagemem,org.apache.ignite.internal.processors.clus
 ter";version="2.10.0",org.apache.ignite.internal.pagemem.wal;uses:="o
 rg.apache.ignite,org.apache.ignite.internal.pagemem.wal.record,org.ap
 ache.ignite.internal.processors.cache,org.apache.ignite.internal.proc
 essors.cache.persistence,org.apache.ignite.internal.processors.cache.
 persistence.wal,org.apache.ignite.internal.processors.cluster,org.apa
 che.ignite.internal.util.lang,org.apache.ignite.lang";version="2.10.0
 ",org.apache.ignite.internal.pagemem.wal.record;uses:="org.apache.ign
 ite.internal.managers.encryption,org.apache.ignite.internal.pagemem,o
 rg.apache.ignite.internal.processors.cache,org.apache.ignite.internal
 .processors.cache.mvcc,org.apache.ignite.internal.processors.cache.pe
 rsistence.wal,org.apache.ignite.internal.processors.cache.version,org
 .apache.ignite.internal.util.tostring,org.apache.ignite.internal.util
 .typedef,org.apache.ignite.transactions";version="2.10.0",org.apache.
 ignite.internal.pagemem.wal.record.delta;uses:="org.apache.ignite,org
 .apache.ignite.internal.pagemem,org.apache.ignite.internal.pagemem.wa
 l.record,org.apache.ignite.internal.processors.cache.distributed.dht.
 topology,org.apache.ignite.internal.processors.cache.persistence.tree
 .io,org.apache.ignite.internal.util.tostring,org.apache.ignite.lang";
 version="2.10.0",org.apache.ignite.internal.processors;uses:="org.apa
 che.ignite,org.apache.ignite.cluster,org.apache.ignite.internal,org.a
 pache.ignite.internal.util.tostring,org.apache.ignite.lang,org.apache
 .ignite.spi,org.apache.ignite.spi.discovery";version="2.10.0",org.apa
 che.ignite.internal.processors.affinity;uses:="org.apache.ignite,org.
 apache.ignite.cache.affinity,org.apache.ignite.cluster,org.apache.ign
 ite.events,org.apache.ignite.internal,org.apache.ignite.internal.dto,
 org.apache.ignite.internal.managers.discovery,org.apache.ignite.inter
 nal.processors,org.apache.ignite.internal.processors.cache,org.apache
 .ignite.lang,org.apache.ignite.plugin.extensions.communication";versi
 on="2.10.0",org.apache.ignite.internal.processors.authentication;uses
 :="org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.inte
 rnal,org.apache.ignite.internal.managers.discovery,org.apache.ignite.
 internal.processors,org.apache.ignite.internal.processors.affinity,or
 g.apache.ignite.internal.processors.cache.persistence.metastorage,org
 .apache.ignite.lang,org.apache.ignite.plugin.extensions.communication
 ,org.apache.ignite.spi,org.apache.ignite.spi.discovery";version="2.10
 .0",org.apache.ignite.internal.processors.bulkload;uses:="org.apache.
 ignite,org.apache.ignite.internal.processors.query,org.apache.ignite.
 internal.processors.tracing,org.apache.ignite.internal.util.lang,org.
 apache.ignite.lang";version="2.10.0",org.apache.ignite.internal.proce
 ssors.bulkload.pipeline;uses:="org.apache.ignite";version="2.10.0",or
 g.apache.ignite.internal.processors.cache;uses:="javax.cache,javax.ca
 che.configuration,javax.cache.expiry,javax.cache.integration,javax.ca
 che.processor,org.apache.ignite,org.apache.ignite.binary,org.apache.i
 gnite.cache,org.apache.ignite.cache.affinity,org.apache.ignite.cache.
 eviction,org.apache.ignite.cache.query,org.apache.ignite.cache.store,
 org.apache.ignite.cluster,org.apache.ignite.compute,org.apache.ignite
 .configuration,org.apache.ignite.events,org.apache.ignite.internal,or
 g.apache.ignite.internal.cluster,org.apache.ignite.internal.cust.affi
 nityep.processors.cache,org.apache.ignite.internal.cust.affinityep.pr
 ocessors.cache.client,org.apache.ignite.internal.cust.affinityep.proc
 essors.cache.dhtatomic.result.triple,org.apache.ignite.internal.cust.
 affinityep.processors.cache.exception,org.apache.ignite.internal.dto,
 org.apache.ignite.internal.managers.communication,org.apache.ignite.i
 nternal.managers.deployment,org.apache.ignite.internal.managers.disco
 very,org.apache.ignite.internal.managers.eventstorage,org.apache.igni
 te.internal.metric,org.apache.ignite.internal.pagemem.store,org.apach
 e.ignite.internal.pagemem.wal,org.apache.ignite.internal.processors,o
 rg.apache.ignite.internal.processors.affinity,org.apache.ignite.inter
 nal.processors.cache.datastructures,org.apache.ignite.internal.proces
 sors.cache.distributed,org.apache.ignite.internal.processors.cache.di
 stributed.dht,org.apache.ignite.internal.processors.cache.distributed
 .dht.atomic,org.apache.ignite.internal.processors.cache.distributed.d
 ht.colocated,org.apache.ignite.internal.processors.cache.distributed.
 dht.preloader,org.apache.ignite.internal.processors.cache.distributed
 .dht.preloader.latch,org.apache.ignite.internal.processors.cache.dist
 ributed.dht.topology,org.apache.ignite.internal.processors.cache.dist
 ributed.near,org.apache.ignite.internal.processors.cache.dr,org.apach
 e.ignite.internal.processors.cache.extras,org.apache.ignite.internal.
 processors.cache.jta,org.apache.ignite.internal.processors.cache.loca
 l,org.apache.ignite.internal.processors.cache.mvcc,org.apache.ignite.
 internal.processors.cache.persistence,org.apache.ignite.internal.proc
 essors.cache.persistence.diagnostic.pagelocktracker,org.apache.ignite
 .internal.processors.cache.persistence.freelist,org.apache.ignite.int
 ernal.processors.cache.persistence.metastorage,org.apache.ignite.inte
 rnal.processors.cache.persistence.partstate,org.apache.ignite.interna
 l.processors.cache.persistence.partstorage,org.apache.ignite.internal
 .processors.cache.persistence.snapshot,org.apache.ignite.internal.pro
 cessors.cache.persistence.tree.reuse,org.apache.ignite.internal.proce
 ssors.cache.persistence.wal,org.apache.ignite.internal.processors.cac
 he.query,org.apache.ignite.internal.processors.cache.query.continuous
 ,org.apache.ignite.internal.processors.cache.store,org.apache.ignite.
 internal.processors.cache.transactions,org.apache.ignite.internal.pro
 cessors.cache.tree,org.apache.ignite.internal.processors.cache.tree.m
 vcc.data,org.apache.ignite.internal.processors.cache.tree.mvcc.search
 ,org.apache.ignite.internal.processors.cache.version,org.apache.ignit
 e.internal.processors.cache.warmup,org.apache.ignite.internal.process
 ors.cacheobject,org.apache.ignite.internal.processors.closure,org.apa
 che.ignite.internal.processors.cluster,org.apache.ignite.internal.pro
 cessors.dr,org.apache.ignite.internal.processors.metric.impl,org.apac
 he.ignite.internal.processors.platform.cache,org.apache.ignite.intern
 al.processors.plugin,org.apache.ignite.internal.processors.query,org.
 apache.ignite.internal.processors.query.schema,org.apache.ignite.inte
 rnal.processors.query.schema.message,org.apache.ignite.internal.proce
 ssors.query.schema.operation,org.apache.ignite.internal.processors.se
 rvice,org.apache.ignite.internal.processors.timeout,org.apache.ignite
 .internal.sql.optimizer.affinity,org.apache.ignite.internal.util,org.
 apache.ignite.internal.util.future,org.apache.ignite.internal.util.la
 ng,org.apache.ignite.internal.util.tostring,org.apache.ignite.interna
 l.util.typedef,org.apache.ignite.lang,org.apache.ignite.marshaller,or
 g.apache.ignite.marshaller.jdk,org.apache.ignite.mxbean,org.apache.ig
 nite.plugin,org.apache.ignite.plugin.extensions.communication,org.apa
 che.ignite.plugin.security,org.apache.ignite.resources,org.apache.ign
 ite.spi,org.apache.ignite.spi.discovery,org.apache.ignite.spi.encrypt
 ion,org.apache.ignite.transactions";version="2.10.0",org.apache.ignit
 e.internal.processors.cache.affinity;uses:="org.apache.ignite.cache.a
 ffinity,org.apache.ignite.cluster,org.apache.ignite.internal.processo
 rs.cache";version="2.10.0",org.apache.ignite.internal.processors.cach
 e.binary;uses:="org.apache.ignite,org.apache.ignite.binary,org.apache
 .ignite.cluster,org.apache.ignite.configuration,org.apache.ignite.int
 ernal,org.apache.ignite.internal.binary,org.apache.ignite.internal.ma
 nagers.discovery,org.apache.ignite.internal.processors,org.apache.ign
 ite.internal.processors.affinity,org.apache.ignite.internal.processor
 s.cache,org.apache.ignite.internal.processors.cacheobject,org.apache.
 ignite.internal.util.future,org.apache.ignite.lang,org.apache.ignite.
 plugin.extensions.communication,org.apache.ignite.spi,org.apache.igni
 te.spi.discovery";version="2.10.0",org.apache.ignite.internal.process
 ors.cache.datastructures;uses:="org.apache.ignite,org.apache.ignite.i
 nternal.processors.cache,org.apache.ignite.internal.processors.datast
 ructures,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.i
 nternal.processors.cache.distributed;uses:="javax.cache.expiry,org.ap
 ache.ignite,org.apache.ignite.cache,org.apache.ignite.cluster,org.apa
 che.ignite.internal,org.apache.ignite.internal.processors.affinity,or
 g.apache.ignite.internal.processors.cache,org.apache.ignite.internal.
 processors.cache.distributed.dht.topology,org.apache.ignite.internal.
 processors.cache.transactions,org.apache.ignite.internal.processors.c
 ache.version,org.apache.ignite.internal.processors.task,org.apache.ig
 nite.internal.util.lang,org.apache.ignite.internal.util.tostring,org.
 apache.ignite.lang,org.apache.ignite.plugin.extensions.communication,
 org.apache.ignite.transactions";version="2.10.0",org.apache.ignite.in
 ternal.processors.cache.distributed.dht;uses:="javax.cache,javax.cach
 e.expiry,javax.cache.processor,org.apache.ignite,org.apache.ignite.ca
 che,org.apache.ignite.cluster,org.apache.ignite.internal,org.apache.i
 gnite.internal.cluster,org.apache.ignite.internal.cust.affinityep.pro
 cessors.cache.exception,org.apache.ignite.internal.managers.discovery
 ,org.apache.ignite.internal.processors.affinity,org.apache.ignite.int
 ernal.processors.cache,org.apache.ignite.internal.processors.cache.di
 stributed,org.apache.ignite.internal.processors.cache.distributed.dht
 .preloader,org.apache.ignite.internal.processors.cache.distributed.dh
 t.topology,org.apache.ignite.internal.processors.cache.distributed.ne
 ar,org.apache.ignite.internal.processors.cache.extras,org.apache.igni
 te.internal.processors.cache.mvcc,org.apache.ignite.internal.processo
 rs.cache.transactions,org.apache.ignite.internal.processors.cache.ver
 sion,org.apache.ignite.internal.processors.query,org.apache.ignite.in
 ternal.processors.timeout,org.apache.ignite.internal.processors.traci
 ng,org.apache.ignite.internal.transactions,org.apache.ignite.internal
 .util,org.apache.ignite.internal.util.future,org.apache.ignite.intern
 al.util.tostring,org.apache.ignite.internal.util.typedef,org.apache.i
 gnite.lang,org.apache.ignite.plugin.extensions.communication,org.apac
 he.ignite.transactions";version="2.10.0",org.apache.ignite.internal.p
 rocessors.cache.distributed.dht.atomic;uses:="javax.cache.expiry,java
 x.cache.processor,org.apache.ignite,org.apache.ignite.cache,org.apach
 e.ignite.cluster,org.apache.ignite.internal,org.apache.ignite.interna
 l.processors.affinity,org.apache.ignite.internal.processors.cache,org
 .apache.ignite.internal.processors.cache.distributed.dht,org.apache.i
 gnite.internal.processors.cache.distributed.near,org.apache.ignite.in
 ternal.processors.cache.dr,org.apache.ignite.internal.processors.cach
 e.mvcc,org.apache.ignite.internal.processors.cache.transactions,org.a
 pache.ignite.internal.processors.cache.version,org.apache.ignite.inte
 rnal.util,org.apache.ignite.internal.util.tostring,org.apache.ignite.
 internal.util.typedef,org.apache.ignite.lang,org.apache.ignite.plugin
 .extensions.communication,org.apache.ignite.transactions";version="2.
 10.0",org.apache.ignite.internal.processors.cache.distributed.dht.col
 ocated;uses:="org.apache.ignite,org.apache.ignite.internal,org.apache
 .ignite.internal.processors.affinity,org.apache.ignite.internal.proce
 ssors.cache,org.apache.ignite.internal.processors.cache.distributed,o
 rg.apache.ignite.internal.processors.cache.distributed.dht,org.apache
 .ignite.internal.processors.cache.distributed.near,org.apache.ignite.
 internal.processors.cache.mvcc,org.apache.ignite.internal.processors.
 cache.persistence,org.apache.ignite.internal.processors.cache.persist
 ence.wal,org.apache.ignite.internal.processors.cache.transactions,org
 .apache.ignite.internal.processors.cache.version,org.apache.ignite.la
 ng,org.apache.ignite.transactions";version="2.10.0",org.apache.ignite
 .internal.processors.cache.distributed.dht.preloader;uses:="org.apach
 e.ignite,org.apache.ignite.cluster,org.apache.ignite.events,org.apach
 e.ignite.internal,org.apache.ignite.internal.managers.discovery,org.a
 pache.ignite.internal.processors.affinity,org.apache.ignite.internal.
 processors.cache,org.apache.ignite.internal.processors.cache.distribu
 ted.dht,org.apache.ignite.internal.processors.cache.distributed.dht.a
 tomic,org.apache.ignite.internal.processors.cache.distributed.dht.top
 ology,org.apache.ignite.internal.processors.cache.persistence,org.apa
 che.ignite.internal.processors.cache.version,org.apache.ignite.intern
 al.processors.tracing,org.apache.ignite.internal.util,org.apache.igni
 te.internal.util.future,org.apache.ignite.internal.util.lang,org.apac
 he.ignite.internal.util.typedef,org.apache.ignite.lang,org.apache.ign
 ite.plugin.extensions.communication";version="2.10.0",org.apache.igni
 te.internal.processors.cache.distributed.dht.preloader.latch;uses:="o
 rg.apache.ignite,org.apache.ignite.internal,org.apache.ignite.interna
 l.processors.affinity,org.apache.ignite.lang,org.apache.ignite.plugin
 .extensions.communication";version="2.10.0",org.apache.ignite.interna
 l.processors.cache.distributed.dht.topology;uses:="org.apache.ignite,
 org.apache.ignite.cache,org.apache.ignite.cluster,org.apache.ignite.i
 nternal,org.apache.ignite.internal.managers.discovery,org.apache.igni
 te.internal.processors.affinity,org.apache.ignite.internal.processors
 .cache,org.apache.ignite.internal.processors.cache.distributed.dht,or
 g.apache.ignite.internal.processors.cache.distributed.dht.preloader,o
 rg.apache.ignite.internal.processors.cache.transactions,org.apache.ig
 nite.internal.processors.cache.version,org.apache.ignite.internal.pro
 cessors.timeout,org.apache.ignite.internal.util,org.apache.ignite.int
 ernal.util.future,org.apache.ignite.internal.util.tostring,org.apache
 .ignite.internal.util.typedef,org.apache.ignite.internal.util.typedef
 .internal,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.
 internal.processors.cache.distributed.near;uses:="javax.cache,javax.c
 ache.expiry,javax.cache.processor,org.apache.ignite,org.apache.ignite
 .cache,org.apache.ignite.cluster,org.apache.ignite.internal,org.apach
 e.ignite.internal.processors.affinity,org.apache.ignite.internal.proc
 essors.cache,org.apache.ignite.internal.processors.cache.distributed,
 org.apache.ignite.internal.processors.cache.distributed.dht,org.apach
 e.ignite.internal.processors.cache.distributed.dht.atomic,org.apache.
 ignite.internal.processors.cache.dr,org.apache.ignite.internal.proces
 sors.cache.mvcc,org.apache.ignite.internal.processors.cache.persisten
 ce,org.apache.ignite.internal.processors.cache.persistence.wal,org.ap
 ache.ignite.internal.processors.cache.transactions,org.apache.ignite.
 internal.processors.cache.version,org.apache.ignite.internal.processo
 rs.query,org.apache.ignite.internal.processors.timeout,org.apache.ign
 ite.internal.processors.tracing,org.apache.ignite.internal.transactio
 ns,org.apache.ignite.internal.util.future,org.apache.ignite.internal.
 util.tostring,org.apache.ignite.lang,org.apache.ignite.plugin.extensi
 ons.communication,org.apache.ignite.transactions";version="2.10.0",or
 g.apache.ignite.internal.processors.cache.distributed.near.consistenc
 y;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache.ignit
 e.internal,org.apache.ignite.internal.processors.affinity,org.apache.
 ignite.internal.processors.cache,org.apache.ignite.internal.processor
 s.cache.distributed.dht,org.apache.ignite.internal.processors.cache.t
 ransactions,org.apache.ignite.internal.util.future";version="2.10.0",
 org.apache.ignite.internal.processors.cache.dr;uses:="javax.cache.pro
 cessor,org.apache.ignite,org.apache.ignite.internal.processors.affini
 ty,org.apache.ignite.internal.processors.cache,org.apache.ignite.inte
 rnal.processors.cache.version,org.apache.ignite.internal.processors.d
 r,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.internal
 .processors.cache.extras;uses:="org.apache.ignite.internal.processors
 .cache,org.apache.ignite.internal.processors.cache.version";version="
 2.10.0",org.apache.ignite.internal.processors.cache.jta;uses:="org.ap
 ache.ignite,org.apache.ignite.configuration,org.apache.ignite.interna
 l.processors.cache";version="2.10.0",org.apache.ignite.internal.proce
 ssors.cache.local;uses:="org.apache.ignite,org.apache.ignite.cache,or
 g.apache.ignite.internal,org.apache.ignite.internal.processors.cache,
 org.apache.ignite.internal.processors.cache.transactions,org.apache.i
 gnite.internal.processors.cache.version,org.apache.ignite.lang,org.ap
 ache.ignite.transactions";version="2.10.0",org.apache.ignite.internal
 .processors.cache.local.atomic;uses:="javax.cache.processor,org.apach
 e.ignite,org.apache.ignite.internal,org.apache.ignite.internal.proces
 sors.cache,org.apache.ignite.internal.processors.cache.local,org.apac
 he.ignite.internal.processors.cache.transactions,org.apache.ignite.in
 ternal.processors.cache.version,org.apache.ignite.transactions";versi
 on="2.10.0",org.apache.ignite.internal.processors.cache.mvcc;uses:="o
 rg.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.configur
 ation,org.apache.ignite.events,org.apache.ignite.internal,org.apache.
 ignite.internal.cluster,org.apache.ignite.internal.managers.discovery
 ,org.apache.ignite.internal.processors,org.apache.ignite.internal.pro
 cessors.affinity,org.apache.ignite.internal.processors.cache,org.apac
 he.ignite.internal.processors.cache.distributed.dht.topology,org.apac
 he.ignite.internal.processors.cache.distributed.near,org.apache.ignit
 e.internal.processors.cache.persistence,org.apache.ignite.internal.pr
 ocessors.cache.persistence.tree.io,org.apache.ignite.internal.process
 ors.cache.query.continuous,org.apache.ignite.internal.processors.cach
 e.transactions,org.apache.ignite.internal.processors.cache.version,or
 g.apache.ignite.internal.processors.timeout,org.apache.ignite.interna
 l.transactions,org.apache.ignite.internal.util.future,org.apache.igni
 te.lang,org.apache.ignite.plugin.extensions.communication,org.apache.
 ignite.spi";version="2.10.0",org.apache.ignite.internal.processors.ca
 che.mvcc.msg;uses:="org.apache.ignite.internal,org.apache.ignite.inte
 rnal.processors.affinity,org.apache.ignite.internal.processors.cache,
 org.apache.ignite.internal.processors.cache.distributed.dht,org.apach
 e.ignite.internal.processors.cache.mvcc,org.apache.ignite.internal.ut
 il,org.apache.ignite.lang,org.apache.ignite.plugin.extensions.communi
 cation";version="2.10.0",org.apache.ignite.internal.processors.cache.
 mvcc.txlog;uses:="org.apache.ignite,org.apache.ignite.internal,org.ap
 ache.ignite.internal.pagemem,org.apache.ignite.internal.pagemem.wal,o
 rg.apache.ignite.internal.processors.cache.persistence,org.apache.ign
 ite.internal.processors.cache.persistence.checkpoint,org.apache.ignit
 e.internal.processors.cache.persistence.tree,org.apache.ignite.intern
 al.processors.cache.persistence.tree.io,org.apache.ignite.internal.pr
 ocessors.cache.persistence.tree.reuse,org.apache.ignite.internal.proc
 essors.cache.persistence.tree.util,org.apache.ignite.internal.process
 ors.failure";version="2.10.0",org.apache.ignite.internal.processors.c
 ache.persistence;uses:="javax.cache.processor,org.apache.ignite,org.a
 pache.ignite.configuration,org.apache.ignite.internal,org.apache.igni
 te.internal.cust.affinityep.processors.cache.dhtatomic.result.triple,
 org.apache.ignite.internal.mem,org.apache.ignite.internal.metric,org.
 apache.ignite.internal.pagemem,org.apache.ignite.internal.pagemem.sto
 re,org.apache.ignite.internal.pagemem.wal,org.apache.ignite.internal.
 pagemem.wal.record,org.apache.ignite.internal.processors.affinity,org
 .apache.ignite.internal.processors.cache,org.apache.ignite.internal.p
 rocessors.cache.distributed.dht.preloader,org.apache.ignite.internal.
 processors.cache.mvcc,org.apache.ignite.internal.processors.cache.per
 sistence.checkpoint,org.apache.ignite.internal.processors.cache.persi
 stence.defragmentation,org.apache.ignite.internal.processors.cache.pe
 rsistence.evict,org.apache.ignite.internal.processors.cache.persisten
 ce.file,org.apache.ignite.internal.processors.cache.persistence.freel
 ist,org.apache.ignite.internal.processors.cache.persistence.metastora
 ge,org.apache.ignite.internal.processors.cache.persistence.pagemem,or
 g.apache.ignite.internal.processors.cache.persistence.partstate,org.a
 pache.ignite.internal.processors.cache.persistence.partstorage,org.ap
 ache.ignite.internal.processors.cache.persistence.tree,org.apache.ign
 ite.internal.processors.cache.persistence.tree.io,org.apache.ignite.i
 nternal.processors.cache.persistence.tree.reuse,org.apache.ignite.int
 ernal.processors.cache.persistence.tree.util,org.apache.ignite.intern
 al.processors.cache.persistence.wal,org.apache.ignite.internal.proces
 sors.cache.tree,org.apache.ignite.internal.processors.cache.tree.mvcc
 .data,org.apache.ignite.internal.processors.cache.tree.mvcc.search,or
 g.apache.ignite.internal.processors.cache.version,org.apache.ignite.i
 nternal.processors.cluster,org.apache.ignite.internal.processors.fail
 ure,org.apache.ignite.internal.processors.metric,org.apache.ignite.in
 ternal.processors.metric.impl,org.apache.ignite.internal.processors.q
 uery,org.apache.ignite.internal.util,org.apache.ignite.internal.util.
 lang,org.apache.ignite.internal.util.tostring,org.apache.ignite.inter
 nal.util.typedef,org.apache.ignite.lang,org.apache.ignite.maintenance
 ,org.apache.ignite.mxbean";version="2.10.0",org.apache.ignite.interna
 l.processors.cache.persistence.checkpoint;uses:="org.apache.ignite,or
 g.apache.ignite.configuration,org.apache.ignite.internal,org.apache.i
 gnite.internal.pagemem,org.apache.ignite.internal.pagemem.store,org.a
 pache.ignite.internal.pagemem.wal,org.apache.ignite.internal.pagemem.
 wal.record,org.apache.ignite.internal.processors.cache,org.apache.ign
 ite.internal.processors.cache.persistence,org.apache.ignite.internal.
 processors.cache.persistence.file,org.apache.ignite.internal.processo
 rs.cache.persistence.pagemem,org.apache.ignite.internal.processors.ca
 che.persistence.partstate,org.apache.ignite.internal.processors.cache
 .persistence.snapshot,org.apache.ignite.internal.processors.cache.per
 sistence.wal,org.apache.ignite.internal.processors.failure,org.apache
 .ignite.internal.util,org.apache.ignite.internal.util.future,org.apac
 he.ignite.internal.util.lang,org.apache.ignite.internal.util.tostring
 ,org.apache.ignite.internal.util.typedef,org.apache.ignite.internal.u
 til.worker,org.apache.ignite.internal.worker,org.apache.ignite.lang";
 version="2.10.0",org.apache.ignite.internal.processors.cache.persiste
 nce.defragmentation;uses:="org.apache.ignite,org.apache.ignite.intern
 al,org.apache.ignite.internal.pagemem,org.apache.ignite.internal.proc
 essors.cache,org.apache.ignite.internal.processors.cache.persistence,
 org.apache.ignite.internal.processors.cache.persistence.checkpoint,or
 g.apache.ignite.internal.processors.cache.persistence.file,org.apache
 .ignite.internal.processors.cache.persistence.pagemem,org.apache.igni
 te.internal.processors.cache.persistence.tree,org.apache.ignite.inter
 nal.processors.cache.persistence.tree.io,org.apache.ignite.mxbean";ve
 rsion="2.10.0",org.apache.ignite.internal.processors.cache.persistenc
 e.defragmentation.maintenance;uses:="org.apache.ignite,org.apache.ign
 ite.internal.processors.cache.persistence.defragmentation,org.apache.
 ignite.internal.processors.failure,org.apache.ignite.maintenance";ver
 sion="2.10.0",org.apache.ignite.internal.processors.cache.persistence
 .diagnostic.pagelocktracker;uses:="org.apache.ignite,org.apache.ignit
 e.internal.processors.cache.persistence.diagnostic.pagelocktracker.lo
 g,org.apache.ignite.internal.processors.cache.persistence.diagnostic.
 pagelocktracker.stack,org.apache.ignite.internal.processors.cache.per
 sistence.tree.util,org.apache.ignite.lang,org.apache.ignite.lifecycle
 ,org.apache.ignite.mxbean";version="2.10.0",org.apache.ignite.interna
 l.processors.cache.persistence.diagnostic.pagelocktracker.dumpprocess
 ors;uses:="org.apache.ignite,org.apache.ignite.internal.processors.ca
 che.persistence.diagnostic.pagelocktracker";version="2.10.0",org.apac
 he.ignite.internal.processors.cache.persistence.diagnostic.pagelocktr
 acker.log;uses:="org.apache.ignite.internal.processors.cache.persiste
 nce.diagnostic.pagelocktracker";version="2.10.0",org.apache.ignite.in
 ternal.processors.cache.persistence.diagnostic.pagelocktracker.stack;
 uses:="org.apache.ignite.internal.processors.cache.persistence.diagno
 stic.pagelocktracker";version="2.10.0",org.apache.ignite.internal.pro
 cessors.cache.persistence.diagnostic.pagelocktracker.store;uses:="org
 .apache.ignite.internal.processors.cache.persistence.diagnostic.pagel
 ocktracker";version="2.10.0",org.apache.ignite.internal.processors.ca
 che.persistence.evict;uses:="org.apache.ignite,org.apache.ignite.conf
 iguration,org.apache.ignite.internal.pagemem,org.apache.ignite.intern
 al.pagemem.impl,org.apache.ignite.internal.processors.cache,org.apach
 e.ignite.lifecycle";version="2.10.0",org.apache.ignite.internal.proce
 ssors.cache.persistence.file;uses:="org.apache.ignite,org.apache.igni
 te.configuration,org.apache.ignite.internal,org.apache.ignite.interna
 l.pagemem.store,org.apache.ignite.internal.processors.cache,org.apach
 e.ignite.internal.processors.cache.persistence,org.apache.ignite.inte
 rnal.util.future,org.apache.ignite.lang";version="2.10.0",org.apache.
 ignite.internal.processors.cache.persistence.filename;uses:="org.apac
 he.ignite,org.apache.ignite.internal,org.apache.ignite.internal.proce
 ssors,org.apache.ignite.internal.processors.cache.persistence";versio
 n="2.10.0",org.apache.ignite.internal.processors.cache.persistence.fr
 eelist;uses:="org.apache.ignite,org.apache.ignite.internal,org.apache
 .ignite.internal.metric,org.apache.ignite.internal.pagemem,org.apache
 .ignite.internal.pagemem.wal,org.apache.ignite.internal.processors.ca
 che.persistence,org.apache.ignite.internal.processors.cache.persisten
 ce.tree.io,org.apache.ignite.internal.processors.cache.persistence.tr
 ee.reuse,org.apache.ignite.internal.processors.cache.persistence.tree
 .util,org.apache.ignite.internal.util";version="2.10.0",org.apache.ig
 nite.internal.processors.cache.persistence.freelist.io;uses:="org.apa
 che.ignite,org.apache.ignite.internal.processors.cache.persistence.fr
 eelist,org.apache.ignite.internal.processors.cache.persistence.tree.i
 o,org.apache.ignite.internal.util";version="2.10.0",org.apache.ignite
 .internal.processors.cache.persistence.metastorage;uses:="org.apache.
 ignite,org.apache.ignite.internal.pagemem,org.apache.ignite.internal.
 pagemem.wal,org.apache.ignite.internal.processors.cache,org.apache.ig
 nite.internal.processors.cache.persistence,org.apache.ignite.internal
 .processors.cache.persistence.checkpoint,org.apache.ignite.internal.p
 rocessors.cache.persistence.freelist,org.apache.ignite.internal.proce
 ssors.cache.persistence.partstorage,org.apache.ignite.internal.proces
 sors.cache.persistence.tree,org.apache.ignite.internal.processors.cac
 he.persistence.tree.io,org.apache.ignite.internal.processors.cache.pe
 rsistence.tree.reuse,org.apache.ignite.internal.processors.cache.pers
 istence.tree.util,org.apache.ignite.internal.processors.failure,org.a
 pache.ignite.lang,org.apache.ignite.marshaller";version="2.10.0",org.
 apache.ignite.internal.processors.cache.persistence.metastorage.pendi
 ngtask;uses:="org.apache.ignite.internal";version="2.10.0",org.apache
 .ignite.internal.processors.cache.persistence.migration;uses:="org.ap
 ache.ignite,org.apache.ignite.lang";version="2.10.0",org.apache.ignit
 e.internal.processors.cache.persistence.pagemem;uses:="org.apache.ign
 ite,org.apache.ignite.internal,org.apache.ignite.internal.mem,org.apa
 che.ignite.internal.metric,org.apache.ignite.internal.pagemem,org.apa
 che.ignite.internal.pagemem.store,org.apache.ignite.internal.processo
 rs.cache,org.apache.ignite.internal.processors.cache.persistence,org.
 apache.ignite.internal.processors.cache.persistence.checkpoint,org.ap
 ache.ignite.internal.util,org.apache.ignite.internal.util.lang,org.ap
 ache.ignite.internal.util.offheap,org.apache.ignite.internal.util.tos
 tring,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.inte
 rnal.processors.cache.persistence.partstate;uses:="org.apache.ignite.
 internal.pagemem";version="2.10.0",org.apache.ignite.internal.process
 ors.cache.persistence.partstorage;uses:="org.apache.ignite,org.apache
 .ignite.internal,org.apache.ignite.internal.metric,org.apache.ignite.
 internal.pagemem.wal,org.apache.ignite.internal.processors.cache.pers
 istence,org.apache.ignite.internal.processors.cache.persistence.freel
 ist,org.apache.ignite.internal.processors.cache.persistence.tree.reus
 e,org.apache.ignite.internal.processors.cache.persistence.tree.util";
 version="2.10.0",org.apache.ignite.internal.processors.cache.persiste
 nce.snapshot;uses:="org.apache.ignite,org.apache.ignite.configuration
 ,org.apache.ignite.events,org.apache.ignite.internal,org.apache.ignit
 e.internal.managers.discovery,org.apache.ignite.internal.pagemem,org.
 apache.ignite.internal.processors.affinity,org.apache.ignite.internal
 .processors.cache,org.apache.ignite.internal.processors.cache.distrib
 uted.dht.preloader,org.apache.ignite.internal.processors.cache.persis
 tence.metastorage,org.apache.ignite.internal.processors.cache.persist
 ence.partstate,org.apache.ignite.internal.processors.cluster,org.apac
 he.ignite.lang,org.apache.ignite.mxbean";version="2.10.0",org.apache.
 ignite.internal.processors.cache.persistence.tree;uses:="org.apache.i
 gnite,org.apache.ignite.failure,org.apache.ignite.internal.cust.affin
 ityep.processors.cache.dhtatomic.result.triple,org.apache.ignite.inte
 rnal.metric,org.apache.ignite.internal.pagemem,org.apache.ignite.inte
 rnal.pagemem.wal,org.apache.ignite.internal.processors.cache.persiste
 nce,org.apache.ignite.internal.processors.cache.persistence.tree.io,o
 rg.apache.ignite.internal.processors.cache.persistence.tree.reuse,org
 .apache.ignite.internal.processors.cache.persistence.tree.util,org.ap
 ache.ignite.internal.processors.failure,org.apache.ignite.internal.ut
 il,org.apache.ignite.internal.util.lang,org.apache.ignite.internal.ut
 il.typedef,org.apache.ignite.lang";version="2.10.0",org.apache.ignite
 .internal.processors.cache.persistence.tree.io;uses:="org.apache.igni
 te,org.apache.ignite.internal.metric,org.apache.ignite.internal.pagem
 em,org.apache.ignite.internal.processors.cache.persistence,org.apache
 .ignite.internal.processors.cache.persistence.freelist,org.apache.ign
 ite.internal.processors.cache.persistence.snapshot,org.apache.ignite.
 internal.processors.cache.persistence.tree,org.apache.ignite.internal
 .processors.cache.version,org.apache.ignite.internal.util,org.apache.
 ignite.lang";version="2.10.0",org.apache.ignite.internal.processors.c
 ache.persistence.tree.reuse;uses:="org.apache.ignite,org.apache.ignit
 e.internal,org.apache.ignite.internal.pagemem,org.apache.ignite.inter
 nal.pagemem.wal,org.apache.ignite.internal.processors.cache.persisten
 ce.freelist,org.apache.ignite.internal.processors.cache.persistence.t
 ree.io,org.apache.ignite.internal.processors.cache.persistence.tree.u
 til,org.apache.ignite.internal.util";version="2.10.0",org.apache.igni
 te.internal.processors.cache.persistence.tree.util;uses:="org.apache.
 ignite,org.apache.ignite.internal.metric,org.apache.ignite.internal.p
 agemem,org.apache.ignite.internal.pagemem.wal,org.apache.ignite.inter
 nal.processors.cache.persistence.tree,org.apache.ignite.internal.proc
 essors.cache.persistence.tree.io";version="2.10.0",org.apache.ignite.
 internal.processors.cache.persistence.wal;uses:="org.apache.ignite,or
 g.apache.ignite.configuration,org.apache.ignite.internal,org.apache.i
 gnite.internal.pagemem.wal,org.apache.ignite.internal.pagemem.wal.rec
 ord,org.apache.ignite.internal.processors.cache,org.apache.ignite.int
 ernal.processors.cache.persistence,org.apache.ignite.internal.process
 ors.cache.persistence.file,org.apache.ignite.internal.processors.cach
 e.persistence.wal.aware,org.apache.ignite.internal.processors.cache.p
 ersistence.wal.filehandle,org.apache.ignite.internal.processors.cache
 .persistence.wal.io,org.apache.ignite.internal.processors.cache.persi
 stence.wal.serializer,org.apache.ignite.internal.util,org.apache.igni
 te.lang";version="2.10.0",org.apache.ignite.internal.processors.cache
 .persistence.wal.aware;uses:="org.apache.ignite,org.apache.ignite.int
 ernal";version="2.10.0",org.apache.ignite.internal.processors.cache.p
 ersistence.wal.crc;uses:="org.apache.ignite";version="2.10.0",org.apa
 che.ignite.internal.processors.cache.persistence.wal.filehandle;uses:
 ="org.apache.ignite,org.apache.ignite.configuration,org.apache.ignite
 .internal.pagemem.wal.record,org.apache.ignite.internal.processors.ca
 che,org.apache.ignite.internal.processors.cache.persistence,org.apach
 e.ignite.internal.processors.cache.persistence.wal,org.apache.ignite.
 internal.processors.cache.persistence.wal.io,org.apache.ignite.intern
 al.processors.cache.persistence.wal.serializer,org.apache.ignite.inte
 rnal.util.worker";version="2.10.0",org.apache.ignite.internal.process
 ors.cache.persistence.wal.io;uses:="org.apache.ignite.internal.proces
 sors.cache.persistence.file,org.apache.ignite.internal.processors.cac
 he.persistence.wal,org.apache.ignite.internal.processors.cache.persis
 tence.wal.aware";version="2.10.0",org.apache.ignite.internal.processo
 rs.cache.persistence.wal.reader;uses:="org.apache.ignite,org.apache.i
 gnite.cluster,org.apache.ignite.configuration,org.apache.ignite.inter
 nal,org.apache.ignite.internal.cust.server,org.apache.ignite.internal
 .managers.checkpoint,org.apache.ignite.internal.managers.collision,or
 g.apache.ignite.internal.managers.communication,org.apache.ignite.int
 ernal.managers.deployment,org.apache.ignite.internal.managers.discove
 ry,org.apache.ignite.internal.managers.encryption,org.apache.ignite.i
 nternal.managers.eventstorage,org.apache.ignite.internal.managers.fai
 lover,org.apache.ignite.internal.managers.indexing,org.apache.ignite.
 internal.managers.loadbalancer,org.apache.ignite.internal.managers.sy
 stemview,org.apache.ignite.internal.pagemem.wal,org.apache.ignite.int
 ernal.pagemem.wal.record,org.apache.ignite.internal.processors.affini
 ty,org.apache.ignite.internal.processors.authentication,org.apache.ig
 nite.internal.processors.cache,org.apache.ignite.internal.processors.
 cache.mvcc,org.apache.ignite.internal.processors.cache.persistence.de
 fragmentation,org.apache.ignite.internal.processors.cache.persistence
 .file,org.apache.ignite.internal.processors.cache.persistence.filenam
 e,org.apache.ignite.internal.processors.cache.persistence.wal,org.apa
 che.ignite.internal.processors.cacheobject,org.apache.ignite.internal
 .processors.closure,org.apache.ignite.internal.processors.cluster,org
 .apache.ignite.internal.processors.compress,org.apache.ignite.interna
 l.processors.configuration.distributed,org.apache.ignite.internal.pro
 cessors.continuous,org.apache.ignite.internal.processors.datastreamer
 ,org.apache.ignite.internal.processors.datastructures,org.apache.igni
 te.internal.processors.diagnostic,org.apache.ignite.internal.processo
 rs.failure,org.apache.ignite.internal.processors.job,org.apache.ignit
 e.internal.processors.jobmetrics,org.apache.ignite.internal.processor
 s.localtask,org.apache.ignite.internal.processors.marshaller,org.apac
 he.ignite.internal.processors.metastorage,org.apache.ignite.internal.
 processors.metric,org.apache.ignite.internal.processors.odbc,org.apac
 he.ignite.internal.processors.performancestatistics,org.apache.ignite
 .internal.processors.platform,org.apache.ignite.internal.processors.p
 lugin,org.apache.ignite.internal.processors.pool,org.apache.ignite.in
 ternal.processors.port,org.apache.ignite.internal.processors.query,or
 g.apache.ignite.internal.processors.resource,org.apache.ignite.intern
 al.processors.rest,org.apache.ignite.internal.processors.schedule,org
 .apache.ignite.internal.processors.security,org.apache.ignite.interna
 l.processors.segmentation,org.apache.ignite.internal.processors.servi
 ce,org.apache.ignite.internal.processors.session,org.apache.ignite.in
 ternal.processors.subscription,org.apache.ignite.internal.processors.
 task,org.apache.ignite.internal.processors.timeout,org.apache.ignite.
 internal.processors.tracing,org.apache.ignite.internal.suggestions,or
 g.apache.ignite.internal.util,org.apache.ignite.internal.util.lang,or
 g.apache.ignite.internal.util.typedef,org.apache.ignite.internal.work
 er,org.apache.ignite.lang,org.apache.ignite.maintenance,org.apache.ig
 nite.plugin,org.apache.ignite.spi,org.apache.ignite.spi.communication
 ,org.apache.ignite.spi.discovery,org.apache.ignite.thread";version="2
 .10.0",org.apache.ignite.internal.processors.cache.persistence.wal.re
 cord;uses:="org.apache.ignite.internal.pagemem.wal.record";version="2
 .10.0",org.apache.ignite.internal.processors.cache.persistence.wal.sc
 anner;uses:="org.apache.ignite,org.apache.ignite.internal.pagemem.wal
 ,org.apache.ignite.internal.pagemem.wal.record,org.apache.ignite.inte
 rnal.processors.cache.persistence.file,org.apache.ignite.internal.pro
 cessors.cache.persistence.wal,org.apache.ignite.internal.processors.c
 ache.persistence.wal.reader,org.apache.ignite.internal.processors.cac
 he.persistence.wal.serializer,org.apache.ignite.internal.util.typedef
 ,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.internal.
 processors.cache.persistence.wal.serializer;uses:="org.apache.ignite,
 org.apache.ignite.internal.pagemem.wal.record,org.apache.ignite.inter
 nal.processors.cache,org.apache.ignite.internal.processors.cache.mvcc
 ,org.apache.ignite.internal.processors.cache.persistence.wal,org.apac
 he.ignite.internal.processors.cache.persistence.wal.io,org.apache.ign
 ite.internal.processors.cacheobject,org.apache.ignite.lang";version="
 2.10.0",org.apache.ignite.internal.processors.cache.persistence.wal.s
 erializer.io;uses:="org.apache.ignite,org.apache.ignite.internal.page
 mem.wal.record,org.apache.ignite.internal.processors.cache.persistenc
 e.wal";version="2.10.0",org.apache.ignite.internal.processors.cache.q
 uery;uses:="org.apache.ignite,org.apache.ignite.cache,org.apache.igni
 te.cache.query,org.apache.ignite.cache.query.annotations,org.apache.i
 gnite.cluster,org.apache.ignite.internal,org.apache.ignite.internal.p
 rocessors.affinity,org.apache.ignite.internal.processors.cache,org.ap
 ache.ignite.internal.processors.cache.distributed.dht.topology,org.ap
 ache.ignite.internal.processors.cache.mvcc,org.apache.ignite.internal
 .processors.cache.persistence,org.apache.ignite.internal.processors.m
 etric,org.apache.ignite.internal.processors.query,org.apache.ignite.i
 nternal.processors.timeout,org.apache.ignite.internal.util,org.apache
 .ignite.internal.util.future,org.apache.ignite.internal.util.lang,org
 .apache.ignite.lang,org.apache.ignite.marshaller,org.apache.ignite.pl
 ugin.extensions.communication,org.apache.ignite.spi";version="2.10.0"
 ,org.apache.ignite.internal.processors.cache.query.continuous;uses:="
 javax.cache,javax.cache.configuration,javax.cache.event,org.apache.ig
 nite,org.apache.ignite.cache,org.apache.ignite.cache.query,org.apache
 .ignite.internal,org.apache.ignite.internal.managers.deployment,org.a
 pache.ignite.internal.processors.affinity,org.apache.ignite.internal.
 processors.cache,org.apache.ignite.internal.processors.cache.distribu
 ted.dht.atomic,org.apache.ignite.internal.processors.cache.transactio
 ns,org.apache.ignite.internal.processors.continuous,org.apache.ignite
 .internal.processors.security,org.apache.ignite.internal.processors.t
 imeout,org.apache.ignite.internal.util,org.apache.ignite.internal.uti
 l.typedef,org.apache.ignite.lang,org.apache.ignite.plugin.extensions.
 communication";version="2.10.0",org.apache.ignite.internal.processors
 .cache.query.jdbc;uses:="org.apache.ignite,org.apache.ignite.cluster,
 org.apache.ignite.compute";version="2.10.0",org.apache.ignite.interna
 l.processors.cache.store;uses:="javax.cache,org.apache.ignite,org.apa
 che.ignite.cache.store,org.apache.ignite.configuration,org.apache.ign
 ite.internal,org.apache.ignite.internal.processors.cache,org.apache.i
 gnite.internal.processors.cache.transactions,org.apache.ignite.intern
 al.processors.cache.version,org.apache.ignite.internal.util.lang,org.
 apache.ignite.lang,org.apache.ignite.lifecycle";version="2.10.0",org.
 apache.ignite.internal.processors.cache.transactions;uses:="javax.cac
 he.expiry,javax.cache.processor,org.apache.ignite,org.apache.ignite.c
 ache,org.apache.ignite.cluster,org.apache.ignite.internal,org.apache.
 ignite.internal.managers.discovery,org.apache.ignite.internal.process
 ors.affinity,org.apache.ignite.internal.processors.cache,org.apache.i
 gnite.internal.processors.cache.distributed,org.apache.ignite.interna
 l.processors.cache.distributed.dht,org.apache.ignite.internal.process
 ors.cache.distributed.dht.topology,org.apache.ignite.internal.process
 ors.cache.distributed.near,org.apache.ignite.internal.processors.cach
 e.mvcc,org.apache.ignite.internal.processors.cache.store,org.apache.i
 gnite.internal.processors.cache.version,org.apache.ignite.internal.pr
 ocessors.query,org.apache.ignite.internal.transactions,org.apache.ign
 ite.internal.util,org.apache.ignite.internal.util.future,org.apache.i
 gnite.internal.util.lang,org.apache.ignite.internal.util.tostring,org
 .apache.ignite.internal.util.typedef,org.apache.ignite.lang,org.apach
 e.ignite.plugin.extensions.communication,org.apache.ignite.transactio
 ns";version="2.10.0",org.apache.ignite.internal.processors.cache.tree
 ;uses:="org.apache.ignite,org.apache.ignite.internal.metric,org.apach
 e.ignite.internal.pagemem,org.apache.ignite.internal.processors.cache
 ,org.apache.ignite.internal.processors.cache.persistence,org.apache.i
 gnite.internal.processors.cache.persistence.freelist,org.apache.ignit
 e.internal.processors.cache.persistence.tree,org.apache.ignite.intern
 al.processors.cache.persistence.tree.io,org.apache.ignite.internal.pr
 ocessors.cache.persistence.tree.reuse,org.apache.ignite.internal.proc
 essors.cache.persistence.tree.util,org.apache.ignite.internal.process
 ors.cache.tree.mvcc.data,org.apache.ignite.internal.processors.cache.
 version,org.apache.ignite.internal.util.lang,org.apache.ignite.lang";
 version="2.10.0",org.apache.ignite.internal.processors.cache.tree.mvc
 c.data;uses:="org.apache.ignite,org.apache.ignite.internal.pagemem.wa
 l,org.apache.ignite.internal.processors.cache,org.apache.ignite.inter
 nal.processors.cache.mvcc,org.apache.ignite.internal.processors.cache
 .persistence,org.apache.ignite.internal.processors.cache.persistence.
 tree,org.apache.ignite.internal.processors.cache.persistence.tree.io,
 org.apache.ignite.internal.processors.cache.tree,org.apache.ignite.in
 ternal.processors.cache.tree.mvcc.search,org.apache.ignite.internal.p
 rocessors.cache.version,org.apache.ignite.lang";version="2.10.0",org.
 apache.ignite.internal.processors.cache.tree.mvcc.search;uses:="org.a
 pache.ignite,org.apache.ignite.internal.processors.cache,org.apache.i
 gnite.internal.processors.cache.mvcc,org.apache.ignite.internal.proce
 ssors.cache.persistence,org.apache.ignite.internal.processors.cache.p
 ersistence.tree,org.apache.ignite.internal.processors.cache.persisten
 ce.tree.io,org.apache.ignite.internal.processors.cache.tree";version=
 "2.10.0",org.apache.ignite.internal.processors.cache.verify;uses:="or
 g.apache.ignite,org.apache.ignite.cache,org.apache.ignite.cluster,org
 .apache.ignite.compute,org.apache.ignite.internal,org.apache.ignite.i
 nternal.processors.affinity,org.apache.ignite.internal.processors.cac
 he,org.apache.ignite.internal.processors.cache.persistence.file,org.a
 pache.ignite.internal.processors.cache.version,org.apache.ignite.inte
 rnal.processors.task,org.apache.ignite.internal.visor,org.apache.igni
 te.internal.visor.verify,org.apache.ignite.lang,org.apache.ignite.res
 ources";version="2.10.0",org.apache.ignite.internal.processors.cache.
 version;uses:="org.apache.ignite,org.apache.ignite.internal,org.apach
 e.ignite.internal.processors.cache,org.apache.ignite.internal.process
 ors.datastreamer,org.apache.ignite.internal.processors.metric.impl,or
 g.apache.ignite.internal.util.tostring,org.apache.ignite.lang,org.apa
 che.ignite.marshaller,org.apache.ignite.plugin.extensions.communicati
 on";version="2.10.0",org.apache.ignite.internal.processors.cache.warm
 up;uses:="org.apache.ignite,org.apache.ignite.configuration,org.apach
 e.ignite.internal.processors.cache,org.apache.ignite.internal.process
 ors.cache.persistence,org.apache.ignite.mxbean,org.apache.ignite.plug
 in";version="2.10.0",org.apache.ignite.internal.processors.cacheobjec
 t;uses:="org.apache.ignite,org.apache.ignite.binary,org.apache.ignite
 .configuration,org.apache.ignite.internal,org.apache.ignite.internal.
 binary,org.apache.ignite.internal.processors,org.apache.ignite.intern
 al.processors.cache";version="2.10.0",org.apache.ignite.internal.proc
 essors.closure;uses:="org.apache.ignite,org.apache.ignite.binary,org.
 apache.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.int
 ernal,org.apache.ignite.internal.processors,org.apache.ignite.interna
 l.processors.affinity,org.apache.ignite.internal.processors.resource,
 org.apache.ignite.internal.util.lang,org.apache.ignite.internal.util.
 tostring,org.apache.ignite.lang,org.apache.ignite.thread";version="2.
 10.0",org.apache.ignite.internal.processors.cluster;uses:="javax.mana
 gement,org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.
 internal,org.apache.ignite.internal.cluster,org.apache.ignite.interna
 l.managers.discovery,org.apache.ignite.internal.processors,org.apache
 .ignite.internal.processors.affinity,org.apache.ignite.internal.proce
 ssors.cache,org.apache.ignite.internal.processors.cache.persistence.m
 etastorage,org.apache.ignite.internal.processors.cluster.baseline.aut
 oadjust,org.apache.ignite.internal.processors.metastorage,org.apache.
 ignite.internal.processors.service,org.apache.ignite.internal.process
 ors.task,org.apache.ignite.lang,org.apache.ignite.mxbean,org.apache.i
 gnite.plugin.extensions.communication,org.apache.ignite.spi,org.apach
 e.ignite.spi.discovery";version="2.10.0",org.apache.ignite.internal.p
 rocessors.cluster.baseline.autoadjust;uses:="org.apache.ignite.events
 ,org.apache.ignite.internal,org.apache.ignite.internal.managers.event
 storage";version="2.10.0",org.apache.ignite.internal.processors.compr
 ess;uses:="org.apache.ignite,org.apache.ignite.configuration,org.apac
 he.ignite.internal,org.apache.ignite.internal.processors";version="2.
 10.0",org.apache.ignite.internal.processors.configuration.distributed
 ;uses:="org.apache.ignite,org.apache.ignite.internal,org.apache.ignit
 e.internal.processors,org.apache.ignite.internal.util.future,org.apac
 he.ignite.lang";version="2.10.0",org.apache.ignite.internal.processor
 s.continuous;uses:="javax.cache.event,org.apache.ignite,org.apache.ig
 nite.cache,org.apache.ignite.cluster,org.apache.ignite.internal,org.a
 pache.ignite.internal.managers.discovery,org.apache.ignite.internal.p
 rocessors,org.apache.ignite.internal.processors.affinity,org.apache.i
 gnite.internal.processors.cache,org.apache.ignite.internal.util.typed
 ef,org.apache.ignite.lang,org.apache.ignite.plugin.extensions.communi
 cation,org.apache.ignite.spi.discovery,org.apache.ignite.util.deque";
 version="2.10.0",org.apache.ignite.internal.processors.datastreamer;u
 ses:="javax.cache,org.apache.ignite,org.apache.ignite.cluster,org.apa
 che.ignite.configuration,org.apache.ignite.internal,org.apache.ignite
 .internal.processors,org.apache.ignite.internal.processors.affinity,o
 rg.apache.ignite.internal.processors.cache,org.apache.ignite.internal
 .util.tostring,org.apache.ignite.lang,org.apache.ignite.plugin.extens
 ions.communication,org.apache.ignite.stream";version="2.10.0",org.apa
 che.ignite.internal.processors.datastructures;uses:="javax.cache.proc
 essor,org.apache.ignite,org.apache.ignite.configuration,org.apache.ig
 nite.internal,org.apache.ignite.internal.processors,org.apache.ignite
 .internal.processors.cache,org.apache.ignite.internal.processors.clus
 ter,org.apache.ignite.internal.util.lang,org.apache.ignite.lang";vers
 ion="2.10.0",org.apache.ignite.internal.processors.diagnostic;uses:="
 org.apache.ignite,org.apache.ignite.failure,org.apache.ignite.interna
 l,org.apache.ignite.internal.processors,org.apache.ignite.internal.ut
 il.tostring,org.apache.ignite.internal.util.typedef";version="2.10.0"
 ,org.apache.ignite.internal.processors.dr;uses:="org.apache.ignite,or
 g.apache.ignite.internal.processors.cache,org.apache.ignite.internal.
 processors.datastreamer,org.apache.ignite.stream";version="2.10.0",or
 g.apache.ignite.internal.processors.failure;uses:="org.apache.ignite,
 org.apache.ignite.failure,org.apache.ignite.internal,org.apache.ignit
 e.internal.processors";version="2.10.0",org.apache.ignite.internal.pr
 ocessors.job;uses:="org.apache.ignite,org.apache.ignite.cluster,org.a
 pache.ignite.compute,org.apache.ignite.internal,org.apache.ignite.int
 ernal.managers.deployment,org.apache.ignite.internal.processors,org.a
 pache.ignite.internal.processors.affinity,org.apache.ignite.internal.
 processors.cache.distributed.dht,org.apache.ignite.internal.processor
 s.timeout,org.apache.ignite.internal.util.worker,org.apache.ignite.la
 ng";version="2.10.0",org.apache.ignite.internal.processors.jobmetrics
 ;uses:="org.apache.ignite,org.apache.ignite.internal,org.apache.ignit
 e.internal.processors";version="2.10.0",org.apache.ignite.internal.pr
 ocessors.localtask;uses:="org.apache.ignite,org.apache.ignite.configu
 ration,org.apache.ignite.internal,org.apache.ignite.internal.processo
 rs,org.apache.ignite.internal.processors.cache.persistence.checkpoint
 ,org.apache.ignite.internal.processors.cache.persistence.metastorage,
 org.apache.ignite.internal.processors.cache.persistence.metastorage.p
 endingtask,org.apache.ignite.internal.processors.cluster";version="2.
 10.0",org.apache.ignite.internal.processors.marshaller;uses:="org.apa
 che.ignite,org.apache.ignite.internal,org.apache.ignite.internal.mana
 gers.discovery,org.apache.ignite.internal.processors,org.apache.ignit
 e.internal.processors.affinity,org.apache.ignite.internal.util.future
 ,org.apache.ignite.lang,org.apache.ignite.plugin.extensions.communica
 tion,org.apache.ignite.spi.discovery";version="2.10.0",org.apache.ign
 ite.internal.processors.metastorage;uses:="org.apache.ignite,org.apac
 he.ignite.internal,org.apache.ignite.internal.util.future";version="2
 .10.0",org.apache.ignite.internal.processors.metastorage.persistence;
 uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.
 internal,org.apache.ignite.internal.processors,org.apache.ignite.inte
 rnal.processors.cluster,org.apache.ignite.internal.processors.metasto
 rage,org.apache.ignite.internal.util.future,org.apache.ignite.lang,or
 g.apache.ignite.spi,org.apache.ignite.spi.discovery";version="2.10.0"
 ,org.apache.ignite.internal.processors.metric;uses:="org.apache.ignit
 e,org.apache.ignite.internal,org.apache.ignite.internal.managers,org.
 apache.ignite.internal.processors.metric.impl,org.apache.ignite.inter
 nal.util,org.apache.ignite.mxbean,org.apache.ignite.spi,org.apache.ig
 nite.spi.metric,org.apache.ignite.thread";version="2.10.0",org.apache
 .ignite.internal.processors.metric.impl;uses:="org.apache.ignite.inte
 rnal.processors.metric,org.apache.ignite.internal.util.typedef,org.ap
 ache.ignite.spi.metric";version="2.10.0",org.apache.ignite.internal.p
 rocessors.nodevalidation;uses:="org.apache.ignite.cluster,org.apache.
 ignite.internal,org.apache.ignite.internal.processors,org.apache.igni
 te.spi";version="2.10.0",org.apache.ignite.internal.processors.odbc;u
 ses:="org.apache.ignite,org.apache.ignite.binary,org.apache.ignite.co
 nfiguration,org.apache.ignite.failure,org.apache.ignite.internal,org.
 apache.ignite.internal.binary,org.apache.ignite.internal.binary.strea
 ms,org.apache.ignite.internal.cust.server.node.link,org.apache.ignite
 .internal.processors,org.apache.ignite.internal.processors.authentica
 tion,org.apache.ignite.internal.processors.security,org.apache.ignite
 .internal.util,org.apache.ignite.internal.util.nio,org.apache.ignite.
 plugin.extensions.communication";version="2.10.0",org.apache.ignite.i
 nternal.processors.odbc.jdbc;uses:="org.apache.ignite,org.apache.igni
 te.binary,org.apache.ignite.cache,org.apache.ignite.internal,org.apac
 he.ignite.internal.binary,org.apache.ignite.internal.jdbc.thin,org.ap
 ache.ignite.internal.processors.affinity,org.apache.ignite.internal.p
 rocessors.authentication,org.apache.ignite.internal.processors.bulklo
 ad,org.apache.ignite.internal.processors.odbc,org.apache.ignite.inter
 nal.processors.query,org.apache.ignite.internal.sql.optimizer.affinit
 y,org.apache.ignite.internal.util,org.apache.ignite.internal.util.nio
 ";version="2.10.0",org.apache.ignite.internal.processors.odbc.link.me
 trics;uses:="org.apache.ignite.internal.cust.server.base.metrics,org.
 apache.ignite.internal.util.nio,org.apache.ignite.mxbean";version="2.
 10.0",org.apache.ignite.internal.processors.odbc.link.util;uses:="org
 .apache.ignite.internal.util.nio";version="2.10.0",org.apache.ignite.
 internal.processors.odbc.odbc;uses:="org.apache.ignite,org.apache.ign
 ite.binary,org.apache.ignite.cache.query,org.apache.ignite.internal,o
 rg.apache.ignite.internal.binary,org.apache.ignite.internal.processor
 s.authentication,org.apache.ignite.internal.processors.odbc,org.apach
 e.ignite.internal.processors.query,org.apache.ignite.internal.util,or
 g.apache.ignite.internal.util.nio";version="2.10.0",org.apache.ignite
 .internal.processors.odbc.odbc.escape;version="2.10.0",org.apache.ign
 ite.internal.processors.offheap;uses:="org.apache.ignite,org.apache.i
 gnite.internal,org.apache.ignite.internal.processors,org.apache.ignit
 e.internal.processors.cache,org.apache.ignite.internal.util.lang,org.
 apache.ignite.internal.util.offheap,org.apache.ignite.internal.util.t
 ypedef,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.int
 ernal.processors.performancestatistics;uses:="org.apache.ignite,org.a
 pache.ignite.internal,org.apache.ignite.internal.processors,org.apach
 e.ignite.internal.processors.cache.query,org.apache.ignite.internal.u
 til,org.apache.ignite.lang,org.apache.ignite.mxbean";version="2.10.0"
 ,org.apache.ignite.internal.processors.platform;uses:="org.apache.ign
 ite,org.apache.ignite.binary,org.apache.ignite.cluster,org.apache.ign
 ite.configuration,org.apache.ignite.events,org.apache.ignite.internal
 ,org.apache.ignite.internal.binary,org.apache.ignite.internal.logger.
 platform,org.apache.ignite.internal.processors,org.apache.ignite.inte
 rnal.processors.affinity,org.apache.ignite.internal.processors.cache.
 distributed.dht.preloader,org.apache.ignite.internal.processors.platf
 orm.cache,org.apache.ignite.internal.processors.platform.cache.query,
 org.apache.ignite.internal.processors.platform.cache.store,org.apache
 .ignite.internal.processors.platform.callback,org.apache.ignite.inter
 nal.processors.platform.cluster,org.apache.ignite.internal.processors
 .platform.compute,org.apache.ignite.internal.processors.platform.data
 streamer,org.apache.ignite.internal.processors.platform.memory,org.ap
 ache.ignite.internal.processors.platform.message,org.apache.ignite.in
 ternal.processors.platform.utils,org.apache.ignite.internal.processor
 s.resource,org.apache.ignite.lang,org.apache.ignite.platform,org.apac
 he.ignite.plugin";version="2.10.0",org.apache.ignite.internal.process
 ors.platform.binary;uses:="org.apache.ignite,org.apache.ignite.intern
 al.binary,org.apache.ignite.internal.processors.platform";version="2.
 10.0",org.apache.ignite.internal.processors.platform.cache;uses:="jav
 ax.cache,javax.cache.processor,org.apache.ignite,org.apache.ignite.bi
 nary,org.apache.ignite.cache,org.apache.ignite.cache.query,org.apache
 .ignite.internal.binary,org.apache.ignite.internal.processors.cache,o
 rg.apache.ignite.internal.processors.platform,org.apache.ignite.inter
 nal.processors.platform.callback,org.apache.ignite.internal.processor
 s.platform.memory,org.apache.ignite.internal.processors.platform.util
 s,org.apache.ignite.lang,org.apache.ignite.resources";version="2.10.0
 ",org.apache.ignite.internal.processors.platform.cache.affinity;uses:
 ="org.apache.ignite,org.apache.ignite.binary,org.apache.ignite.cache.
 affinity,org.apache.ignite.cluster,org.apache.ignite.internal.binary,
 org.apache.ignite.internal.processors.platform,org.apache.ignite.life
 cycle,org.apache.ignite.resources";version="2.10.0",org.apache.ignite
 .internal.processors.platform.cache.expiry;uses:="javax.cache.configu
 ration,javax.cache.expiry";version="2.10.0",org.apache.ignite.interna
 l.processors.platform.cache.query;uses:="javax.cache,javax.cache.even
 t,org.apache.ignite,org.apache.ignite.cache,org.apache.ignite.cache.q
 uery,org.apache.ignite.internal.binary,org.apache.ignite.internal.pro
 cessors.cache,org.apache.ignite.internal.processors.cache.query,org.a
 pache.ignite.internal.processors.platform";version="2.10.0",org.apach
 e.ignite.internal.processors.platform.cache.store;version="2.10.0",or
 g.apache.ignite.internal.processors.platform.callback;uses:="org.apac
 he.ignite,org.apache.ignite.internal.processors.affinity,org.apache.i
 gnite.internal.processors.platform,org.apache.ignite.internal.process
 ors.platform.memory";version="2.10.0",org.apache.ignite.internal.proc
 essors.platform.client;uses:="org.apache.ignite,org.apache.ignite.bin
 ary,org.apache.ignite.configuration,org.apache.ignite.internal,org.ap
 ache.ignite.internal.binary,org.apache.ignite.internal.processors.aff
 inity,org.apache.ignite.internal.processors.odbc,org.apache.ignite.in
 ternal.processors.platform.client.tx,org.apache.ignite.internal.util.
 nio";version="2.10.0",org.apache.ignite.internal.processors.platform.
 client.binary;uses:="org.apache.ignite.binary,org.apache.ignite.inter
 nal.binary,org.apache.ignite.internal.processors.platform.client";ver
 sion="2.10.0",org.apache.ignite.internal.processors.platform.client.c
 ache;uses:="javax.cache.event,org.apache.ignite.binary,org.apache.ign
 ite.cache,org.apache.ignite.cache.query,org.apache.ignite.internal,or
 g.apache.ignite.internal.binary,org.apache.ignite.internal.processors
 .affinity,org.apache.ignite.internal.processors.platform.client,org.a
 pache.ignite.internal.processors.platform.client.tx,org.apache.ignite
 .lang";version="2.10.0",org.apache.ignite.internal.processors.platfor
 m.client.cluster;uses:="org.apache.ignite.binary,org.apache.ignite.cl
 uster,org.apache.ignite.internal.binary,org.apache.ignite.internal.pr
 ocessors.platform.client";version="2.10.0",org.apache.ignite.internal
 .processors.platform.client.compute;uses:="org.apache.ignite.internal
 .binary,org.apache.ignite.internal.processors.platform.client";versio
 n="2.10.0",org.apache.ignite.internal.processors.platform.client.eppr
 oxy;uses:="org.apache.ignite.internal.binary,org.apache.ignite.intern
 al.processors.platform.client";version="2.10.0",org.apache.ignite.int
 ernal.processors.platform.client.service;uses:="org.apache.ignite.int
 ernal.binary,org.apache.ignite.internal.processors.platform.client";v
 ersion="2.10.0",org.apache.ignite.internal.processors.platform.client
 .tx;uses:="org.apache.ignite,org.apache.ignite.binary,org.apache.igni
 te.internal.processors.cache.distributed.near,org.apache.ignite.inter
 nal.processors.platform.client";version="2.10.0",org.apache.ignite.in
 ternal.processors.platform.cluster;uses:="org.apache.ignite,org.apach
 e.ignite.cluster,org.apache.ignite.internal.binary,org.apache.ignite.
 internal.cluster,org.apache.ignite.internal.processors.platform,org.a
 pache.ignite.lang,org.apache.ignite.resources";version="2.10.0",org.a
 pache.ignite.internal.processors.platform.compute;uses:="org.apache.i
 gnite,org.apache.ignite.cluster,org.apache.ignite.compute,org.apache.
 ignite.internal,org.apache.ignite.internal.binary,org.apache.ignite.i
 nternal.processors.platform,org.apache.ignite.internal.processors.pla
 tform.callback,org.apache.ignite.lang,org.apache.ignite.resources";ve
 rsion="2.10.0",org.apache.ignite.internal.processors.platform.cpp;use
 s:="org.apache.ignite.configuration,org.apache.ignite.internal.logger
 .platform,org.apache.ignite.internal.processors.platform,org.apache.i
 gnite.internal.processors.platform.cache,org.apache.ignite.internal.p
 rocessors.platform.callback,org.apache.ignite.internal.processors.pla
 tform.memory,org.apache.ignite.lang,org.apache.ignite.platform.cpp";v
 ersion="2.10.0",org.apache.ignite.internal.processors.platform.datast
 reamer;uses:="org.apache.ignite,org.apache.ignite.internal.binary,org
 .apache.ignite.internal.processors.datastreamer,org.apache.ignite.int
 ernal.processors.platform,org.apache.ignite.resources,org.apache.igni
 te.stream";version="2.10.0",org.apache.ignite.internal.processors.pla
 tform.datastructures;uses:="org.apache.ignite,org.apache.ignite.inter
 nal.binary,org.apache.ignite.internal.processors.datastructures,org.a
 pache.ignite.internal.processors.platform";version="2.10.0",org.apach
 e.ignite.internal.processors.platform.dotnet;uses:="javax.cache,org.a
 pache.ignite,org.apache.ignite.cache.store,org.apache.ignite.configur
 ation,org.apache.ignite.internal,org.apache.ignite.internal.binary,or
 g.apache.ignite.internal.logger.platform,org.apache.ignite.internal.p
 rocessors.platform,org.apache.ignite.internal.processors.platform.cac
 he,org.apache.ignite.internal.processors.platform.cache.store,org.apa
 che.ignite.internal.processors.platform.callback,org.apache.ignite.in
 ternal.processors.platform.memory,org.apache.ignite.internal.processo
 rs.platform.services,org.apache.ignite.internal.util.tostring,org.apa
 che.ignite.lang,org.apache.ignite.lifecycle,org.apache.ignite.platfor
 m.dotnet";version="2.10.0",org.apache.ignite.internal.processors.plat
 form.entityframework;uses:="javax.cache.processor,org.apache.ignite,o
 rg.apache.ignite.binary,org.apache.ignite.cache,org.apache.ignite.int
 ernal.binary,org.apache.ignite.internal.processors.platform.cache,org
 .apache.ignite.internal.processors.platform.memory";version="2.10.0",
 org.apache.ignite.internal.processors.platform.events;uses:="org.apac
 he.ignite,org.apache.ignite.events,org.apache.ignite.internal,org.apa
 che.ignite.internal.binary,org.apache.ignite.internal.processors.plat
 form,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.inter
 nal.processors.platform.lifecycle;uses:="org.apache.ignite.internal.p
 rocessors.platform.callback,org.apache.ignite.lifecycle";version="2.1
 0.0",org.apache.ignite.internal.processors.platform.memory;uses:="org
 .apache.ignite.internal.binary.streams,org.apache.ignite.internal.pro
 cessors.platform.callback";version="2.10.0",org.apache.ignite.interna
 l.processors.platform.message;uses:="org.apache.ignite.internal,org.a
 pache.ignite.lang";version="2.10.0",org.apache.ignite.internal.proces
 sors.platform.messaging;uses:="org.apache.ignite,org.apache.ignite.in
 ternal,org.apache.ignite.internal.binary,org.apache.ignite.internal.p
 rocessors.platform,org.apache.ignite.internal.processors.platform.mes
 sage";version="2.10.0",org.apache.ignite.internal.processors.platform
 .plugin;uses:="org.apache.ignite,org.apache.ignite.internal,org.apach
 e.ignite.internal.processors";version="2.10.0",org.apache.ignite.inte
 rnal.processors.platform.plugin.cache;uses:="org.apache.ignite.plugin
 ";version="2.10.0",org.apache.ignite.internal.processors.platform.ser
 vices;uses:="org.apache.ignite,org.apache.ignite.internal.binary,org.
 apache.ignite.internal.processors.platform,org.apache.ignite.resource
 s,org.apache.ignite.services";version="2.10.0",org.apache.ignite.inte
 rnal.processors.platform.transactions;uses:="org.apache.ignite,org.ap
 ache.ignite.internal.binary,org.apache.ignite.internal.processors.pla
 tform";version="2.10.0",org.apache.ignite.internal.processors.platfor
 m.utils;uses:="javax.cache.configuration,javax.cache.event,javax.cach
 e.expiry,org.apache.ignite,org.apache.ignite.binary,org.apache.ignite
 .cache,org.apache.ignite.configuration,org.apache.ignite.internal,org
 .apache.ignite.internal.binary,org.apache.ignite.internal.processors.
 cache.binary,org.apache.ignite.internal.processors.platform,org.apach
 e.ignite.internal.processors.platform.cache.affinity,org.apache.ignit
 e.lang,org.apache.ignite.platform.dotnet,org.apache.ignite.util";vers
 ion="2.10.0",org.apache.ignite.internal.processors.platform.websessio
 n;uses:="javax.cache.processor,org.apache.ignite,org.apache.ignite.bi
 nary,org.apache.ignite.cache,org.apache.ignite.internal.binary,org.ap
 ache.ignite.internal.processors.platform.cache,org.apache.ignite.inte
 rnal.processors.platform.memory";version="2.10.0",org.apache.ignite.i
 nternal.processors.plugin;uses:="javax.cache,org.apache.ignite,org.ap
 ache.ignite.cluster,org.apache.ignite.configuration,org.apache.ignite
 .internal,org.apache.ignite.internal.processors,org.apache.ignite.int
 ernal.processors.cache,org.apache.ignite.plugin,org.apache.ignite.spi
 .discovery";version="2.10.0",org.apache.ignite.internal.processors.po
 ol;uses:="org.apache.ignite,org.apache.ignite.internal,org.apache.ign
 ite.internal.processors";version="2.10.0",org.apache.ignite.internal.
 processors.port;uses:="org.apache.ignite,org.apache.ignite.internal,o
 rg.apache.ignite.internal.processors,org.apache.ignite.spi";version="
 2.10.0",org.apache.ignite.internal.processors.query;uses:="javax.cach
 e,javax.cache.configuration,org.apache.ignite,org.apache.ignite.cache
 ,org.apache.ignite.cache.affinity,org.apache.ignite.cache.query,org.a
 pache.ignite.cluster,org.apache.ignite.configuration,org.apache.ignit
 e.internal,org.apache.ignite.internal.managers,org.apache.ignite.inte
 rnal.pagemem,org.apache.ignite.internal.processors,org.apache.ignite.
 internal.processors.affinity,org.apache.ignite.internal.processors.ca
 che,org.apache.ignite.internal.processors.cache.distributed.dht.prelo
 ader,org.apache.ignite.internal.processors.cache.mvcc,org.apache.igni
 te.internal.processors.cache.persistence,org.apache.ignite.internal.p
 rocessors.cache.persistence.checkpoint,org.apache.ignite.internal.pro
 cessors.cache.persistence.defragmentation,org.apache.ignite.internal.
 processors.cache.persistence.pagemem,org.apache.ignite.internal.proce
 ssors.cache.persistence.tree.reuse,org.apache.ignite.internal.process
 ors.cache.query,org.apache.ignite.internal.processors.odbc.jdbc,org.a
 pache.ignite.internal.processors.query.property,org.apache.ignite.int
 ernal.processors.query.schema,org.apache.ignite.internal.processors.q
 uery.schema.message,org.apache.ignite.internal.processors.query.schem
 a.operation,org.apache.ignite.internal.processors.tracing,org.apache.
 ignite.internal.util,org.apache.ignite.internal.util.collection,org.a
 pache.ignite.internal.util.future,org.apache.ignite.internal.util.lan
 g,org.apache.ignite.internal.util.worker,org.apache.ignite.lang,org.a
 pache.ignite.spi,org.apache.ignite.spi.discovery,org.apache.ignite.sp
 i.indexing,org.apache.ignite.thread,org.jsr166";version="2.10.0",org.
 apache.ignite.internal.processors.query.h2.twostep.messages;uses:="or
 g.apache.ignite.internal,org.apache.ignite.internal.processors.affini
 ty,org.apache.ignite.plugin.extensions.communication";version="2.10.0
 ",org.apache.ignite.internal.processors.query.messages;uses:="org.apa
 che.ignite.plugin.extensions.communication";version="2.10.0",org.apac
 he.ignite.internal.processors.query.property;uses:="org.apache.ignite
 ,org.apache.ignite.internal,org.apache.ignite.internal.processors.cac
 he,org.apache.ignite.internal.processors.query";version="2.10.0",org.
 apache.ignite.internal.processors.query.schema;uses:="org.apache.igni
 te,org.apache.ignite.cluster,org.apache.ignite.internal,org.apache.ig
 nite.internal.processors.cache,org.apache.ignite.internal.processors.
 cache.distributed.dht.topology,org.apache.ignite.internal.processors.
 cache.persistence,org.apache.ignite.internal.processors.query,org.apa
 che.ignite.internal.processors.query.schema.message,org.apache.ignite
 .internal.processors.query.schema.operation,org.apache.ignite.interna
 l.util.future,org.apache.ignite.internal.util.worker,org.apache.ignit
 e.lang";version="2.10.0",org.apache.ignite.internal.processors.query.
 schema.message;uses:="org.apache.ignite.internal,org.apache.ignite.in
 ternal.managers.discovery,org.apache.ignite.internal.processors.affin
 ity,org.apache.ignite.internal.processors.query.schema,org.apache.ign
 ite.internal.processors.query.schema.operation,org.apache.ignite.inte
 rnal.util.tostring,org.apache.ignite.lang,org.apache.ignite.plugin.ex
 tensions.communication";version="2.10.0",org.apache.ignite.internal.p
 rocessors.query.schema.operation;uses:="org.apache.ignite.cache,org.a
 pache.ignite.internal.processors.query";version="2.10.0",org.apache.i
 gnite.internal.processors.resource;uses:="org.apache.ignite,org.apach
 e.ignite.cache.store,org.apache.ignite.compute,org.apache.ignite.inte
 rnal,org.apache.ignite.internal.managers.deployment,org.apache.ignite
 .internal.processors,org.apache.ignite.lifecycle,org.apache.ignite.se
 rvices,org.apache.ignite.spi";version="2.10.0",org.apache.ignite.inte
 rnal.processors.rest;uses:="org.apache.ignite,org.apache.ignite.inter
 nal,org.apache.ignite.internal.processors,org.apache.ignite.internal.
 processors.rest.handlers,org.apache.ignite.internal.processors.rest.r
 equest,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.int
 ernal.processors.rest.client.message;uses:="org.apache.ignite.cluster
 ,org.apache.ignite.internal.client,org.apache.ignite.plugin.security"
 ;version="2.10.0",org.apache.ignite.internal.processors.rest.handlers
 ;uses:="org.apache.ignite,org.apache.ignite.internal,org.apache.ignit
 e.internal.processors.rest,org.apache.ignite.internal.processors.rest
 .request";version="2.10.0",org.apache.ignite.internal.processors.rest
 .handlers.auth;uses:="org.apache.ignite.internal,org.apache.ignite.in
 ternal.processors.rest,org.apache.ignite.internal.processors.rest.han
 dlers,org.apache.ignite.internal.processors.rest.request";version="2.
 10.0",org.apache.ignite.internal.processors.rest.handlers.beforeStart
 ;uses:="org.apache.ignite.internal,org.apache.ignite.internal.process
 ors.rest,org.apache.ignite.internal.processors.rest.handlers,org.apac
 he.ignite.internal.processors.rest.request";version="2.10.0",org.apac
 he.ignite.internal.processors.rest.handlers.cache;uses:="org.apache.i
 gnite,org.apache.ignite.internal,org.apache.ignite.internal.processor
 s.cache,org.apache.ignite.internal.processors.rest,org.apache.ignite.
 internal.processors.rest.handlers,org.apache.ignite.internal.processo
 rs.rest.request";version="2.10.0",org.apache.ignite.internal.processo
 rs.rest.handlers.cluster;uses:="org.apache.ignite.internal,org.apache
 .ignite.internal.processors.rest,org.apache.ignite.internal.processor
 s.rest.handlers,org.apache.ignite.internal.processors.rest.request";v
 ersion="2.10.0",org.apache.ignite.internal.processors.rest.handlers.d
 atastructures;uses:="org.apache.ignite.internal,org.apache.ignite.int
 ernal.processors.rest,org.apache.ignite.internal.processors.rest.hand
 lers,org.apache.ignite.internal.processors.rest.request";version="2.1
 0.0",org.apache.ignite.internal.processors.rest.handlers.log;uses:="o
 rg.apache.ignite.internal,org.apache.ignite.internal.processors.rest,
 org.apache.ignite.internal.processors.rest.handlers,org.apache.ignite
 .internal.processors.rest.request";version="2.10.0",org.apache.ignite
 .internal.processors.rest.handlers.memory;uses:="org.apache.ignite.in
 ternal,org.apache.ignite.internal.processors.rest,org.apache.ignite.i
 nternal.processors.rest.handlers,org.apache.ignite.internal.processor
 s.rest.request";version="2.10.0",org.apache.ignite.internal.processor
 s.rest.handlers.probe;uses:="org.apache.ignite.internal,org.apache.ig
 nite.internal.processors.rest,org.apache.ignite.internal.processors.r
 est.handlers,org.apache.ignite.internal.processors.rest.request";vers
 ion="2.10.0",org.apache.ignite.internal.processors.rest.handlers.quer
 y;uses:="org.apache.ignite.internal,org.apache.ignite.internal.proces
 sors.query,org.apache.ignite.internal.processors.rest,org.apache.igni
 te.internal.processors.rest.handlers,org.apache.ignite.internal.proce
 ssors.rest.request";version="2.10.0",org.apache.ignite.internal.proce
 ssors.rest.handlers.redis;uses:="org.apache.ignite,org.apache.ignite.
 internal,org.apache.ignite.internal.processors.rest,org.apache.ignite
 .internal.processors.rest.handlers.redis.exception,org.apache.ignite.
 internal.processors.rest.protocols.tcp.redis,org.apache.ignite.intern
 al.processors.rest.request,org.apache.ignite.internal.util.nio";versi
 on="2.10.0",org.apache.ignite.internal.processors.rest.handlers.redis
 .exception;uses:="org.apache.ignite";version="2.10.0",org.apache.igni
 te.internal.processors.rest.handlers.redis.key;uses:="org.apache.igni
 te,org.apache.ignite.internal,org.apache.ignite.internal.processors.r
 est,org.apache.ignite.internal.processors.rest.handlers.redis,org.apa
 che.ignite.internal.processors.rest.protocols.tcp.redis,org.apache.ig
 nite.internal.processors.rest.request";version="2.10.0",org.apache.ig
 nite.internal.processors.rest.handlers.redis.server;uses:="org.apache
 .ignite,org.apache.ignite.internal,org.apache.ignite.internal.process
 ors.rest,org.apache.ignite.internal.processors.rest.handlers.redis,or
 g.apache.ignite.internal.processors.rest.protocols.tcp.redis,org.apac
 he.ignite.internal.processors.rest.request";version="2.10.0",org.apac
 he.ignite.internal.processors.rest.handlers.redis.string;uses:="org.a
 pache.ignite,org.apache.ignite.internal,org.apache.ignite.internal.pr
 ocessors.rest,org.apache.ignite.internal.processors.rest.handlers.red
 is,org.apache.ignite.internal.processors.rest.protocols.tcp.redis,org
 .apache.ignite.internal.processors.rest.request";version="2.10.0",org
 .apache.ignite.internal.processors.rest.handlers.task;uses:="org.apac
 he.ignite.internal,org.apache.ignite.internal.processors.rest,org.apa
 che.ignite.internal.processors.rest.handlers,org.apache.ignite.intern
 al.processors.rest.request,org.apache.ignite.lang,org.apache.ignite.p
 lugin.extensions.communication";version="2.10.0",org.apache.ignite.in
 ternal.processors.rest.handlers.top;uses:="org.apache.ignite.configur
 ation,org.apache.ignite.internal,org.apache.ignite.internal.processor
 s.rest,org.apache.ignite.internal.processors.rest.client.message,org.
 apache.ignite.internal.processors.rest.handlers,org.apache.ignite.int
 ernal.processors.rest.request";version="2.10.0",org.apache.ignite.int
 ernal.processors.rest.handlers.user;uses:="org.apache.ignite.internal
 ,org.apache.ignite.internal.processors.rest,org.apache.ignite.interna
 l.processors.rest.handlers,org.apache.ignite.internal.processors.rest
 .request";version="2.10.0",org.apache.ignite.internal.processors.rest
 .handlers.version;uses:="org.apache.ignite.internal,org.apache.ignite
 .internal.processors.rest,org.apache.ignite.internal.processors.rest.
 handlers,org.apache.ignite.internal.processors.rest.request";version=
 "2.10.0",org.apache.ignite.internal.processors.rest.protocols;uses:="
 org.apache.ignite,org.apache.ignite.configuration,org.apache.ignite.i
 nternal,org.apache.ignite.internal.processors.rest,org.apache.ignite.
 lang";version="2.10.0",org.apache.ignite.internal.processors.rest.pro
 tocols.tcp;uses:="org.apache.ignite,org.apache.ignite.internal,org.ap
 ache.ignite.internal.client.marshaller,org.apache.ignite.internal.pro
 cessors.rest,org.apache.ignite.internal.processors.rest.client.messag
 e,org.apache.ignite.internal.processors.rest.protocols,org.apache.ign
 ite.internal.util.nio,org.apache.ignite.marshaller";version="2.10.0",
 org.apache.ignite.internal.processors.rest.protocols.tcp.redis;uses:=
 "org.apache.ignite,org.apache.ignite.internal,org.apache.ignite.inter
 nal.processors.rest,org.apache.ignite.internal.processors.rest.client
 .message,org.apache.ignite.internal.processors.rest.handlers.redis,or
 g.apache.ignite.internal.util.nio";version="2.10.0",org.apache.ignite
 .internal.processors.rest.request;uses:="org.apache.ignite.cluster,or
 g.apache.ignite.internal.processors.authentication,org.apache.ignite.
 internal.processors.cache,org.apache.ignite.internal.processors.rest"
 ;version="2.10.0",org.apache.ignite.internal.processors.schedule;uses
 :="org.apache.ignite.internal,org.apache.ignite.internal.processors,o
 rg.apache.ignite.scheduler";version="2.10.0",org.apache.ignite.intern
 al.processors.security;uses:="org.apache.ignite,org.apache.ignite.clu
 ster,org.apache.ignite.internal,org.apache.ignite.internal.processors
 ,org.apache.ignite.internal.processors.security.sandbox,org.apache.ig
 nite.lang,org.apache.ignite.marshaller,org.apache.ignite.plugin.secur
 ity,org.apache.ignite.resources,org.apache.ignite.spi,org.apache.igni
 te.spi.discovery";version="2.10.0",org.apache.ignite.internal.process
 ors.security.sandbox;uses:="org.apache.ignite,org.apache.ignite.inter
 nal,org.apache.ignite.internal.processors.security";version="2.10.0",
 org.apache.ignite.internal.processors.segmentation;uses:="org.apache.
 ignite.internal.processors";version="2.10.0",org.apache.ignite.intern
 al.processors.segmentation.os;uses:="org.apache.ignite.internal,org.a
 pache.ignite.internal.processors,org.apache.ignite.internal.processor
 s.segmentation";version="2.10.0",org.apache.ignite.internal.processor
 s.service;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apac
 he.ignite.events,org.apache.ignite.internal,org.apache.ignite.interna
 l.managers.discovery,org.apache.ignite.internal.processors,org.apache
 .ignite.internal.processors.affinity,org.apache.ignite.internal.proce
 ssors.cache,org.apache.ignite.internal.processors.cluster,org.apache.
 ignite.internal.util.future,org.apache.ignite.lang,org.apache.ignite.
 plugin.extensions.communication,org.apache.ignite.services,org.apache
 .ignite.spi.discovery";version="2.10.0",org.apache.ignite.internal.pr
 ocessors.session;uses:="org.apache.ignite,org.apache.ignite.cluster,o
 rg.apache.ignite.compute,org.apache.ignite.internal,org.apache.ignite
 .internal.managers.deployment,org.apache.ignite.internal.processors,o
 rg.apache.ignite.lang";version="2.10.0",org.apache.ignite.internal.pr
 ocessors.subscription;uses:="org.apache.ignite.internal,org.apache.ig
 nite.internal.processors,org.apache.ignite.internal.processors.cache.
 persistence,org.apache.ignite.internal.processors.cache.persistence.m
 etastorage,org.apache.ignite.internal.processors.configuration.distri
 buted,org.apache.ignite.internal.processors.metastorage";version="2.1
 0.0",org.apache.ignite.internal.processors.task;uses:="org.apache.ign
 ite,org.apache.ignite.compute,org.apache.ignite.internal,org.apache.i
 gnite.internal.managers.deployment,org.apache.ignite.internal.process
 ors,org.apache.ignite.internal.processors.cluster,org.apache.ignite.i
 nternal.processors.timeout,org.apache.ignite.internal.util.worker,org
 .apache.ignite.lang";version="2.10.0",org.apache.ignite.internal.proc
 essors.timeout;uses:="org.apache.ignite,org.apache.ignite.internal,or
 g.apache.ignite.internal.processors,org.apache.ignite.lang,org.apache
 .ignite.spi";version="2.10.0",org.apache.ignite.internal.processors.t
 racing;uses:="org.apache.ignite.internal.processors.tracing.messages,
 org.apache.ignite.spi.tracing";version="2.10.0",org.apache.ignite.int
 ernal.processors.tracing.configuration;uses:="org.apache.ignite,org.a
 pache.ignite.internal,org.apache.ignite.internal.processors.configura
 tion.distributed,org.apache.ignite.internal.util.tostring,org.apache.
 ignite.spi.tracing";version="2.10.0",org.apache.ignite.internal.proce
 ssors.tracing.messages;uses:="org.apache.ignite,org.apache.ignite.int
 ernal.processors.tracing";version="2.10.0",org.apache.ignite.internal
 .sql;uses:="org.apache.ignite,org.apache.ignite.internal.sql.command"
 ;version="2.10.0",org.apache.ignite.internal.sql.command;uses:="org.a
 pache.ignite.internal.processors.bulkload,org.apache.ignite.internal.
 sql,org.apache.ignite.internal.util.typedef,org.apache.ignite.lang";v
 ersion="2.10.0",org.apache.ignite.internal.sql.optimizer.affinity;use
 s:="org.apache.ignite,org.apache.ignite.binary,org.apache.ignite.inte
 rnal.binary,org.apache.ignite.internal.processors.affinity,org.apache
 .ignite.internal.util.tostring";version="2.10.0",org.apache.ignite.in
 ternal.suggestions;uses:="org.apache.ignite";version="2.10.0",org.apa
 che.ignite.internal.tck;uses:="javax.management";version="2.10.0",org
 .apache.ignite.internal.tracing;version="2.10.0",org.apache.ignite.in
 ternal.transactions;uses:="org.apache.ignite";version="2.10.0",org.ap
 ache.ignite.internal.util;uses:="javax.crypto,javax.management,javax.
 naming,org.apache.ignite,org.apache.ignite.binary,org.apache.ignite.c
 luster,org.apache.ignite.compute,org.apache.ignite.configuration,org.
 apache.ignite.internal,org.apache.ignite.internal.cluster,org.apache.
 ignite.internal.cust.affinityep.processors.cache.dhtatomic.result.tri
 ple,org.apache.ignite.internal.managers.discovery,org.apache.ignite.i
 nternal.processors.cache,org.apache.ignite.internal.processors.cache.
 distributed.dht.topology,org.apache.ignite.internal.processors.cluste
 r,org.apache.ignite.internal.util.future,org.apache.ignite.internal.u
 til.lang,org.apache.ignite.internal.util.tostring,org.apache.ignite.i
 nternal.util.typedef,org.apache.ignite.internal.util.typedef.internal
 ,org.apache.ignite.internal.util.worker,org.apache.ignite.lang,org.ap
 ache.ignite.marshaller,org.apache.ignite.plugin,org.apache.ignite.plu
 gin.extensions.communication,org.apache.ignite.spi,org.apache.ignite.
 spi.discovery,org.jsr166";version="2.10.0",org.apache.ignite.internal
 .util.collection;uses:="org.apache.ignite.internal.util";version="2.1
 0.0",org.apache.ignite.internal.util.distributed;uses:="org.apache.ig
 nite.internal,org.apache.ignite.internal.managers.discovery,org.apach
 e.ignite.internal.processors.affinity,org.apache.ignite.internal.util
 .typedef,org.apache.ignite.lang,org.apache.ignite.plugin.extensions.c
 ommunication";version="2.10.0",org.apache.ignite.internal.util.functi
 on;version="2.10.0",org.apache.ignite.internal.util.future;uses:="org
 .apache.ignite,org.apache.ignite.cluster,org.apache.ignite.compute,or
 g.apache.ignite.internal,org.apache.ignite.internal.processors.task,o
 rg.apache.ignite.lang";version="2.10.0",org.apache.ignite.internal.ut
 il.gridify;uses:="org.apache.ignite,org.apache.ignite.compute,org.apa
 che.ignite.compute.gridify,org.apache.ignite.compute.gridify.aop";ver
 sion="2.10.0",org.apache.ignite.internal.util.io;uses:="org.apache.ig
 nite.internal.processors.cache.persistence.file";version="2.10.0",org
 .apache.ignite.internal.util.ipc;uses:="org.apache.ignite,org.apache.
 ignite.internal.processors.metric,org.apache.ignite.internal.util.nio
 ";version="2.10.0",org.apache.ignite.internal.util.ipc.loopback;uses:
 ="org.apache.ignite,org.apache.ignite.internal.util.ipc";version="2.1
 0.0",org.apache.ignite.internal.util.ipc.shmem;uses:="org.apache.igni
 te,org.apache.ignite.internal.util.ipc";version="2.10.0",org.apache.i
 gnite.internal.util.lang;uses:="javax.cache,org.apache.ignite,org.apa
 che.ignite.cluster,org.apache.ignite.internal,org.apache.ignite.inter
 nal.util,org.apache.ignite.lang,org.apache.ignite.spi";version="2.10.
 0",org.apache.ignite.internal.util.lang.gridfunc;uses:="org.apache.ig
 nite.cluster,org.apache.ignite.internal.util,org.apache.ignite.intern
 al.util.lang,org.apache.ignite.internal.util.typedef,org.apache.ignit
 e.lang";version="2.10.0",org.apache.ignite.internal.util.nio;uses:="o
 rg.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.failure,
 org.apache.ignite.internal,org.apache.ignite.internal.processors.metr
 ic,org.apache.ignite.internal.processors.metric.impl,org.apache.ignit
 e.internal.processors.tracing,org.apache.ignite.internal.util.future,
 org.apache.ignite.internal.util.lang,org.apache.ignite.internal.util.
 worker,org.apache.ignite.lang,org.apache.ignite.plugin.extensions.com
 munication";version="2.10.0",org.apache.ignite.internal.util.nio.ssl;
 uses:="javax.net.ssl,org.apache.ignite,org.apache.ignite.internal.uti
 l.nio,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.inte
 rnal.util.nodestart;uses:="org.apache.ignite,org.apache.ignite.cluste
 r,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.internal
 .util.offheap;uses:="org.apache.ignite.internal.util.lang,org.apache.
 ignite.internal.util.typedef,org.apache.ignite.lang";version="2.10.0"
 ,org.apache.ignite.internal.util.offheap.unsafe;uses:="org.apache.ign
 ite.internal.util.lang,org.apache.ignite.internal.util.offheap,org.ap
 ache.ignite.internal.util.typedef,org.apache.ignite.lang";version="2.
 10.0",org.apache.ignite.internal.util.scala;version="2.10.0",org.apac
 he.ignite.internal.util.spring;uses:="org.apache.ignite,org.apache.ig
 nite.configuration,org.apache.ignite.internal.processors.resource,org
 .apache.ignite.lang";version="2.10.0",org.apache.ignite.internal.util
 .test;version="2.10.0",org.apache.ignite.internal.util.tostring;uses:
 ="org.apache.ignite.internal.util";version="2.10.0",org.apache.ignite
 .internal.util.typedef;uses:="javax.cache,org.apache.ignite,org.apach
 e.ignite.cluster,org.apache.ignite.events,org.apache.ignite.internal.
 util.lang,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.
 internal.util.typedef.internal;uses:="org.apache.ignite.internal.proc
 essors.cache,org.apache.ignite.internal.util,org.apache.ignite.intern
 al.util.lang,org.apache.ignite.internal.util.tostring";version="2.10.
 0",org.apache.ignite.internal.util.worker;uses:="org.apache.ignite,or
 g.apache.ignite.internal,org.apache.ignite.internal.util.future";vers
 ion="2.10.0",org.apache.ignite.internal.visor;uses:="org.apache.ignit
 e,org.apache.ignite.cluster,org.apache.ignite.compute,org.apache.igni
 te.internal,org.apache.ignite.resources";version="2.10.0",org.apache.
 ignite.internal.visor.annotation;version="2.10.0",org.apache.ignite.i
 nternal.visor.baseline;uses:="org.apache.ignite.cluster,org.apache.ig
 nite.internal.dto,org.apache.ignite.internal.processors.task,org.apac
 he.ignite.internal.visor";version="2.10.0",org.apache.ignite.internal
 .visor.binary;uses:="org.apache.ignite,org.apache.ignite.binary,org.a
 pache.ignite.internal.processors.task,org.apache.ignite.internal.viso
 r";version="2.10.0",org.apache.ignite.internal.visor.cache;uses:="jav
 ax.cache.configuration,org.apache.ignite,org.apache.ignite.cache,org.
 apache.ignite.cache.store.jdbc,org.apache.ignite.compute,org.apache.i
 gnite.configuration,org.apache.ignite.internal,org.apache.ignite.inte
 rnal.dto,org.apache.ignite.internal.processors.cache,org.apache.ignit
 e.internal.processors.cache.distributed.dht.preloader,org.apache.igni
 te.internal.processors.cache.distributed.dht.topology,org.apache.igni
 te.internal.processors.cache.query,org.apache.ignite.internal.process
 ors.task,org.apache.ignite.internal.visor,org.apache.ignite.internal.
 visor.query,org.apache.ignite.lang";version="2.10.0",org.apache.ignit
 e.internal.visor.cache.index;uses:="org.apache.ignite,org.apache.igni
 te.compute,org.apache.ignite.configuration,org.apache.ignite.internal
 .dto,org.apache.ignite.internal.processors.cache,org.apache.ignite.in
 ternal.processors.task,org.apache.ignite.internal.visor";version="2.1
 0.0",org.apache.ignite.internal.visor.compute;uses:="org.apache.ignit
 e,org.apache.ignite.cluster,org.apache.ignite.compute,org.apache.igni
 te.internal,org.apache.ignite.internal.processors.task,org.apache.ign
 ite.internal.visor,org.apache.ignite.lang,org.apache.ignite.resources
 ";version="2.10.0",org.apache.ignite.internal.visor.debug;uses:="org.
 apache.ignite.internal.processors.task,org.apache.ignite.internal.vis
 or";version="2.10.0",org.apache.ignite.internal.visor.defragmentation
 ;uses:="org.apache.ignite,org.apache.ignite.compute,org.apache.ignite
 .internal.dto,org.apache.ignite.internal.processors.task,org.apache.i
 gnite.internal.visor";version="2.10.0",org.apache.ignite.internal.vis
 or.diagnostic;uses:="org.apache.ignite,org.apache.ignite.cluster,org.
 apache.ignite.compute,org.apache.ignite.internal.dto,org.apache.ignit
 e.internal.processors.task,org.apache.ignite.internal.visor";version=
 "2.10.0",org.apache.ignite.internal.visor.diagnostic.availability;use
 s:="org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.com
 pute,org.apache.ignite.internal.dto,org.apache.ignite.internal.proces
 sors.task,org.apache.ignite.internal.visor";version="2.10.0",org.apac
 he.ignite.internal.visor.encryption;uses:="org.apache.ignite,org.apac
 he.ignite.compute,org.apache.ignite.internal.dto,org.apache.ignite.in
 ternal.processors.cache,org.apache.ignite.internal.processors.task,or
 g.apache.ignite.internal.visor";version="2.10.0",org.apache.ignite.in
 ternal.visor.event;uses:="org.apache.ignite.internal.visor,org.apache
 .ignite.lang";version="2.10.0",org.apache.ignite.internal.visor.file;
 uses:="org.apache.ignite.internal.processors.task,org.apache.ignite.i
 nternal.visor,org.apache.ignite.internal.visor.log";version="2.10.0",
 org.apache.ignite.internal.visor.igfs;uses:="org.apache.ignite.intern
 al.processors.task,org.apache.ignite.internal.visor";version="2.10.0"
 ,org.apache.ignite.internal.visor.log;uses:="org.apache.ignite,org.ap
 ache.ignite.compute,org.apache.ignite.internal.processors.task,org.ap
 ache.ignite.internal.visor";version="2.10.0",org.apache.ignite.intern
 al.visor.metric;uses:="org.apache.ignite.internal.dto,org.apache.igni
 te.internal.processors.task,org.apache.ignite.internal.visor";version
 ="2.10.0",org.apache.ignite.internal.visor.misc;uses:="org.apache.ign
 ite,org.apache.ignite.cluster,org.apache.ignite.compute,org.apache.ig
 nite.internal.dto,org.apache.ignite.internal.processors.task,org.apac
 he.ignite.internal.visor";version="2.10.0",org.apache.ignite.internal
 .visor.node;uses:="org.apache.ignite,org.apache.ignite.binary,org.apa
 che.ignite.cache,org.apache.ignite.compute,org.apache.ignite.configur
 ation,org.apache.ignite.internal,org.apache.ignite.internal.dto,org.a
 pache.ignite.internal.processors.affinity,org.apache.ignite.internal.
 processors.task,org.apache.ignite.internal.visor,org.apache.ignite.in
 ternal.visor.cache,org.apache.ignite.internal.visor.event,org.apache.
 ignite.internal.visor.igfs,org.apache.ignite.internal.visor.util,org.
 apache.ignite.lang,org.apache.ignite.plugin.segmentation,org.apache.i
 gnite.services,org.apache.ignite.transactions";version="2.10.0",org.a
 pache.ignite.internal.visor.persistence;uses:="org.apache.ignite.inte
 rnal.dto,org.apache.ignite.internal.processors.task,org.apache.ignite
 .internal.visor,org.apache.ignite.lang";version="2.10.0",org.apache.i
 gnite.internal.visor.query;uses:="org.apache.ignite,org.apache.ignite
 .binary,org.apache.ignite.cache,org.apache.ignite.cache.query,org.apa
 che.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.config
 uration,org.apache.ignite.internal,org.apache.ignite.internal.dto,org
 .apache.ignite.internal.processors.cache.query,org.apache.ignite.inte
 rnal.processors.query,org.apache.ignite.internal.processors.task,org.
 apache.ignite.internal.visor,org.apache.ignite.lang";version="2.10.0"
 ,org.apache.ignite.internal.visor.service;uses:="org.apache.ignite.in
 ternal.processors.task,org.apache.ignite.internal.visor,org.apache.ig
 nite.services";version="2.10.0",org.apache.ignite.internal.visor.shut
 down;uses:="org.apache.ignite,org.apache.ignite.internal.dto,org.apac
 he.ignite.internal.processors.task,org.apache.ignite.internal.visor";
 version="2.10.0",org.apache.ignite.internal.visor.snapshot;uses:="org
 .apache.ignite.internal.processors.task,org.apache.ignite.internal.vi
 sor";version="2.10.0",org.apache.ignite.internal.visor.systemview;use
 s:="org.apache.ignite.internal.dto,org.apache.ignite.internal.process
 ors.task,org.apache.ignite.internal.visor";version="2.10.0",org.apach
 e.ignite.internal.visor.tracing.configuration;uses:="org.apache.ignit
 e.internal.dto,org.apache.ignite.internal.processors.task,org.apache.
 ignite.internal.visor,org.apache.ignite.spi.tracing";version="2.10.0"
 ,org.apache.ignite.internal.visor.tx;uses:="org.apache.ignite,org.apa
 che.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.intern
 al.dto,org.apache.ignite.internal.processors.affinity,org.apache.igni
 te.internal.processors.cache.version,org.apache.ignite.internal.proce
 ssors.task,org.apache.ignite.internal.visor,org.apache.ignite.lang,or
 g.apache.ignite.transactions";version="2.10.0",org.apache.ignite.inte
 rnal.visor.util;uses:="javax.cache.configuration,org.apache.ignite,or
 g.apache.ignite.cluster,org.apache.ignite.events,org.apache.ignite.in
 ternal,org.apache.ignite.internal.visor.event,org.apache.ignite.inter
 nal.visor.file,org.apache.ignite.internal.visor.log,org.apache.ignite
 .lang";version="2.10.0",org.apache.ignite.internal.visor.verify;uses:
 ="org.apache.ignite,org.apache.ignite.compute,org.apache.ignite.inter
 nal.dto,org.apache.ignite.internal.processors.cache.verify,org.apache
 .ignite.internal.processors.task,org.apache.ignite.internal.visor";ve
 rsion="2.10.0",org.apache.ignite.internal.websession;uses:="javax.cac
 he.processor,org.apache.ignite.binary";version="2.10.0",org.apache.ig
 nite.internal.worker;uses:="org.apache.ignite,org.apache.ignite.failu
 re,org.apache.ignite.internal.processors.cache.persistence,org.apache
 .ignite.internal.util.worker,org.apache.ignite.lang,org.apache.ignite
 .mxbean";version="2.10.0",org.apache.ignite.lang;uses:="org.apache.ig
 nite,org.apache.ignite.binary,org.apache.ignite.internal.util.lang";v
 ersion="2.10.0",org.apache.ignite.lifecycle;uses:="org.apache.ignite"
 ;version="2.10.0",org.apache.ignite.logger;uses:="org.apache.ignite";
 version="2.10.0",org.apache.ignite.logger.java;uses:="org.apache.igni
 te,org.apache.ignite.logger";version="2.10.0",org.apache.ignite.maint
 enance;uses:="org.apache.ignite,org.apache.ignite.internal.util.lang"
 ;version="2.10.0",org.apache.ignite.marshaller;uses:="org.apache.igni
 te,org.apache.ignite.lang,org.apache.ignite.marshaller.jdk,org.apache
 .ignite.plugin";version="2.10.0",org.apache.ignite.marshaller.jdk;use
 s:="org.apache.ignite,org.apache.ignite.lang,org.apache.ignite.marsha
 ller";version="2.10.0",org.apache.ignite.messaging;uses:="org.apache.
 ignite,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.mxb
 ean;uses:="javax.cache.management,javax.management,org.apache.ignite,
 org.apache.ignite.cache,org.apache.ignite.cluster,org.apache.ignite.t
 ransactions";version="2.10.0",org.apache.ignite.platform;version="2.1
 0.0",org.apache.ignite.platform.cpp;uses:="org.apache.ignite.configur
 ation";version="2.10.0",org.apache.ignite.platform.dotnet;uses:="java
 x.cache.configuration,org.apache.ignite,org.apache.ignite.cache.affin
 ity,org.apache.ignite.cluster,org.apache.ignite.configuration,org.apa
 che.ignite.internal.processors.platform.cache.affinity,org.apache.ign
 ite.internal.processors.platform.dotnet,org.apache.ignite.internal.pr
 ocessors.platform.lifecycle,org.apache.ignite.lifecycle";version="2.1
 0.0",org.apache.ignite.plugin;uses:="javax.cache,org.apache.ignite,or
 g.apache.ignite.cluster,org.apache.ignite.configuration,org.apache.ig
 nite.marshaller,org.apache.ignite.spi";version="2.10.0",org.apache.ig
 nite.plugin.extensions.communication;uses:="org.apache.ignite,org.apa
 che.ignite.internal.processors.affinity,org.apache.ignite.lang,org.ap
 ache.ignite.plugin";version="2.10.0",org.apache.ignite.plugin.platfor
 m;uses:="org.apache.ignite.binary,org.apache.ignite.configuration,org
 .apache.ignite.lang";version="2.10.0",org.apache.ignite.plugin.securi
 ty;uses:="org.apache.ignite,org.apache.ignite.internal.processors.aut
 hentication";version="2.10.0",org.apache.ignite.plugin.segmentation;u
 ses:="org.apache.ignite";version="2.10.0",org.apache.ignite.resources
 ;version="2.10.0",org.apache.ignite.scheduler;uses:="org.apache.ignit
 e,org.apache.ignite.lang";version="2.10.0",org.apache.ignite.services
 ;uses:="org.apache.ignite,org.apache.ignite.cluster,org.apache.ignite
 .internal.util.tostring,org.apache.ignite.lang";version="2.10.0",org.
 apache.ignite.spi;uses:="javax.cache,org.apache.ignite,org.apache.ign
 ite.cluster,org.apache.ignite.events,org.apache.ignite.internal.manag
 ers.communication,org.apache.ignite.internal.managers.eventstorage,or
 g.apache.ignite.internal.util,org.apache.ignite.lang,org.apache.ignit
 e.mxbean,org.apache.ignite.plugin.extensions.communication,org.apache
 .ignite.plugin.security,org.apache.ignite.resources,org.apache.ignite
 .spi.discovery,org.apache.ignite.spi.metric,org.apache.ignite.thread"
 ;version="2.10.0",org.apache.ignite.spi.checkpoint;uses:="org.apache.
 ignite.spi";version="2.10.0",org.apache.ignite.spi.checkpoint.cache;u
 ses:="org.apache.ignite.mxbean,org.apache.ignite.spi,org.apache.ignit
 e.spi.checkpoint";version="2.10.0",org.apache.ignite.spi.checkpoint.j
 dbc;uses:="javax.sql,org.apache.ignite.mxbean,org.apache.ignite.spi,o
 rg.apache.ignite.spi.checkpoint";version="2.10.0",org.apache.ignite.s
 pi.checkpoint.noop;uses:="org.apache.ignite.spi,org.apache.ignite.spi
 .checkpoint";version="2.10.0",org.apache.ignite.spi.checkpoint.shared
 fs;uses:="org.apache.ignite.mxbean,org.apache.ignite.spi,org.apache.i
 gnite.spi.checkpoint";version="2.10.0",org.apache.ignite.spi.collisio
 n;uses:="org.apache.ignite.compute,org.apache.ignite.spi";version="2.
 10.0",org.apache.ignite.spi.collision.fifoqueue;uses:="org.apache.ign
 ite.mxbean,org.apache.ignite.spi,org.apache.ignite.spi.collision";ver
 sion="2.10.0",org.apache.ignite.spi.collision.jobstealing;uses:="org.
 apache.ignite.mxbean,org.apache.ignite.plugin.extensions.communicatio
 n,org.apache.ignite.spi,org.apache.ignite.spi.collision";version="2.1
 0.0",org.apache.ignite.spi.collision.noop;uses:="org.apache.ignite.sp
 i,org.apache.ignite.spi.collision";version="2.10.0",org.apache.ignite
 .spi.collision.priorityqueue;uses:="org.apache.ignite.mxbean,org.apac
 he.ignite.spi,org.apache.ignite.spi.collision";version="2.10.0",org.a
 pache.ignite.spi.communication;uses:="org.apache.ignite.cluster,org.a
 pache.ignite.lang,org.apache.ignite.spi";version="2.10.0",org.apache.
 ignite.spi.communication.tcp;uses:="org.apache.ignite,org.apache.igni
 te.cluster,org.apache.ignite.internal,org.apache.ignite.internal.proc
 essors.metric,org.apache.ignite.internal.util.nio,org.apache.ignite.l
 ang,org.apache.ignite.mxbean,org.apache.ignite.plugin.extensions.comm
 unication,org.apache.ignite.spi,org.apache.ignite.spi.communication,o
 rg.apache.ignite.spi.communication.tcp.internal";version="2.10.0",org
 .apache.ignite.spi.communication.tcp.internal;uses:="javax.net.ssl,or
 g.apache.ignite,org.apache.ignite.cluster,org.apache.ignite.configura
 tion,org.apache.ignite.events,org.apache.ignite.failure,org.apache.ig
 nite.internal,org.apache.ignite.internal.managers.discovery,org.apach
 e.ignite.internal.managers.eventstorage,org.apache.ignite.internal.pr
 ocessors.affinity,org.apache.ignite.internal.processors.failure,org.a
 pache.ignite.internal.processors.metric,org.apache.ignite.internal.pr
 ocessors.timeout,org.apache.ignite.internal.processors.tracing,org.ap
 ache.ignite.internal.util,org.apache.ignite.internal.util.function,or
 g.apache.ignite.internal.util.future,org.apache.ignite.internal.util.
 ipc.shmem,org.apache.ignite.internal.util.nio,org.apache.ignite.inter
 nal.util.worker,org.apache.ignite.internal.worker,org.apache.ignite.l
 ang,org.apache.ignite.plugin.extensions.communication,org.apache.igni
 te.resources,org.apache.ignite.spi,org.apache.ignite.spi.communicatio
 n,org.apache.ignite.spi.communication.tcp,org.apache.ignite.spi.commu
 nication.tcp.messages";version="2.10.0",org.apache.ignite.spi.communi
 cation.tcp.internal.shmem;uses:="org.apache.ignite,org.apache.ignite.
 cluster,org.apache.ignite.internal.processors.tracing,org.apache.igni
 te.internal.util.ipc,org.apache.ignite.internal.util.ipc.shmem,org.ap
 ache.ignite.internal.util.lang,org.apache.ignite.internal.util.nio,or
 g.apache.ignite.internal.util.worker,org.apache.ignite.plugin.extensi
 ons.communication,org.apache.ignite.spi.communication.tcp,org.apache.
 ignite.spi.communication.tcp.internal";version="2.10.0",org.apache.ig
 nite.spi.communication.tcp.messages;uses:="org.apache.ignite.internal
 ,org.apache.ignite.plugin.extensions.communication";version="2.10.0",
 org.apache.ignite.spi.deployment;uses:="org.apache.ignite.spi";versio
 n="2.10.0",org.apache.ignite.spi.deployment.local;uses:="org.apache.i
 gnite,org.apache.ignite.mxbean,org.apache.ignite.spi,org.apache.ignit
 e.spi.deployment";version="2.10.0",org.apache.ignite.spi.discovery;us
 es:="org.apache.ignite,org.apache.ignite.cache,org.apache.ignite.clus
 ter,org.apache.ignite.internal.processors.security,org.apache.ignite.
 internal.processors.tracing.messages,org.apache.ignite.internal.util.
 tostring,org.apache.ignite.internal.util.worker,org.apache.ignite.lan
 g,org.apache.ignite.mxbean,org.apache.ignite.plugin.security,org.apac
 he.ignite.spi";version="2.10.0",org.apache.ignite.spi.discovery.isola
 ted;uses:="org.apache.ignite,org.apache.ignite.cache,org.apache.ignit
 e.cluster,org.apache.ignite.internal,org.apache.ignite.internal.manag
 ers.discovery,org.apache.ignite.lang,org.apache.ignite.spi,org.apache
 .ignite.spi.discovery";version="2.10.0",org.apache.ignite.spi.discove
 ry.tcp;uses:="javax.net.ssl,org.apache.ignite,org.apache.ignite.clust
 er,org.apache.ignite.configuration,org.apache.ignite.internal,org.apa
 che.ignite.internal.managers.discovery,org.apache.ignite.internal.uti
 l.worker,org.apache.ignite.lang,org.apache.ignite.marshaller,org.apac
 he.ignite.mxbean,org.apache.ignite.resources,org.apache.ignite.spi,or
 g.apache.ignite.spi.discovery,org.apache.ignite.spi.discovery.tcp.int
 ernal,org.apache.ignite.spi.discovery.tcp.ipfinder,org.apache.ignite.
 spi.discovery.tcp.messages";version="2.10.0",org.apache.ignite.spi.di
 scovery.tcp.internal;uses:="org.apache.ignite,org.apache.ignite.cache
 ,org.apache.ignite.cluster,org.apache.ignite.internal.managers.discov
 ery,org.apache.ignite.internal.processors.metric,org.apache.ignite.in
 ternal.util.future,org.apache.ignite.internal.util.lang,org.apache.ig
 nite.lang,org.apache.ignite.marshaller,org.apache.ignite.spi.discover
 y,org.apache.ignite.spi.discovery.tcp.messages";version="2.10.0",org.
 apache.ignite.spi.discovery.tcp.ipfinder;uses:="org.apache.ignite,org
 .apache.ignite.internal.util.tostring,org.apache.ignite.resources,org
 .apache.ignite.spi";version="2.10.0",org.apache.ignite.spi.discovery.
 tcp.ipfinder.jdbc;uses:="javax.sql,org.apache.ignite.spi,org.apache.i
 gnite.spi.discovery.tcp.ipfinder";version="2.10.0",org.apache.ignite.
 spi.discovery.tcp.ipfinder.multicast;uses:="org.apache.ignite.spi,org
 .apache.ignite.spi.discovery.tcp.ipfinder,org.apache.ignite.spi.disco
 very.tcp.ipfinder.vm";version="2.10.0",org.apache.ignite.spi.discover
 y.tcp.ipfinder.sharedfs;uses:="org.apache.ignite.spi,org.apache.ignit
 e.spi.discovery.tcp.ipfinder";version="2.10.0",org.apache.ignite.spi.
 discovery.tcp.ipfinder.vm;uses:="org.apache.ignite.spi,org.apache.ign
 ite.spi.discovery.tcp.ipfinder";version="2.10.0",org.apache.ignite.sp
 i.discovery.tcp.messages;uses:="org.apache.ignite.cache,org.apache.ig
 nite.cluster,org.apache.ignite.internal.processors.tracing.messages,o
 rg.apache.ignite.internal.util.typedef,org.apache.ignite.lang,org.apa
 che.ignite.marshaller,org.apache.ignite.spi.discovery,org.apache.igni
 te.spi.discovery.tcp.internal";version="2.10.0",org.apache.ignite.spi
 .encryption;uses:="org.apache.ignite,org.apache.ignite.spi";version="
 2.10.0",org.apache.ignite.spi.encryption.keystore;uses:="org.apache.i
 gnite,org.apache.ignite.resources,org.apache.ignite.spi,org.apache.ig
 nite.spi.encryption";version="2.10.0",org.apache.ignite.spi.encryptio
 n.noop;uses:="org.apache.ignite,org.apache.ignite.spi,org.apache.igni
 te.spi.encryption";version="2.10.0",org.apache.ignite.spi.eventstorag
 e;uses:="org.apache.ignite.events,org.apache.ignite.lang,org.apache.i
 gnite.spi";version="2.10.0",org.apache.ignite.spi.eventstorage.memory
 ;uses:="org.apache.ignite.events,org.apache.ignite.lang,org.apache.ig
 nite.mxbean,org.apache.ignite.spi,org.apache.ignite.spi.eventstorage"
 ;version="2.10.0",org.apache.ignite.spi.failover;uses:="org.apache.ig
 nite,org.apache.ignite.cluster,org.apache.ignite.compute,org.apache.i
 gnite.spi";version="2.10.0",org.apache.ignite.spi.failover.always;use
 s:="org.apache.ignite.cluster,org.apache.ignite.mxbean,org.apache.ign
 ite.spi,org.apache.ignite.spi.failover";version="2.10.0",org.apache.i
 gnite.spi.failover.jobstealing;uses:="org.apache.ignite.cluster,org.a
 pache.ignite.mxbean,org.apache.ignite.spi,org.apache.ignite.spi.failo
 ver";version="2.10.0",org.apache.ignite.spi.failover.never;uses:="org
 .apache.ignite.cluster,org.apache.ignite.mxbean,org.apache.ignite.spi
 ,org.apache.ignite.spi.failover";version="2.10.0",org.apache.ignite.s
 pi.indexing;uses:="javax.cache,org.apache.ignite.cluster,org.apache.i
 gnite.internal,org.apache.ignite.internal.processors.affinity,org.apa
 che.ignite.internal.processors.cache,org.apache.ignite.spi";version="
 2.10.0",org.apache.ignite.spi.indexing.noop;uses:="javax.cache,org.ap
 ache.ignite.spi,org.apache.ignite.spi.indexing";version="2.10.0",org.
 apache.ignite.spi.loadbalancing;uses:="org.apache.ignite,org.apache.i
 gnite.cluster,org.apache.ignite.compute,org.apache.ignite.spi";versio
 n="2.10.0",org.apache.ignite.spi.loadbalancing.adaptive;uses:="org.ap
 ache.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.mxbea
 n,org.apache.ignite.spi,org.apache.ignite.spi.loadbalancing";version=
 "2.10.0",org.apache.ignite.spi.loadbalancing.roundrobin;uses:="org.ap
 ache.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.mxbea
 n,org.apache.ignite.spi,org.apache.ignite.spi.loadbalancing";version=
 "2.10.0",org.apache.ignite.spi.loadbalancing.weightedrandom;uses:="or
 g.apache.ignite.cluster,org.apache.ignite.compute,org.apache.ignite.m
 xbean,org.apache.ignite.spi,org.apache.ignite.spi.loadbalancing";vers
 ion="2.10.0",org.apache.ignite.spi.metric;uses:="org.apache.ignite.sp
 i";version="2.10.0",org.apache.ignite.spi.metric.jmx;uses:="javax.man
 agement,org.apache.ignite.spi,org.apache.ignite.spi.metric";version="
 2.10.0",org.apache.ignite.spi.metric.log;uses:="org.apache.ignite.int
 ernal.processors.metric";version="2.10.0",org.apache.ignite.spi.metri
 c.noop;uses:="org.apache.ignite.spi,org.apache.ignite.spi.metric";ver
 sion="2.10.0",org.apache.ignite.spi.systemview;uses:="org.apache.igni
 te.spi,org.apache.ignite.spi.systemview.view";version="2.10.0",org.ap
 ache.ignite.spi.systemview.view;uses:="org.apache.ignite.cache,org.ap
 ache.ignite.cluster,org.apache.ignite.configuration,org.apache.ignite
 .internal.binary,org.apache.ignite.internal.managers.systemview.walke
 r,org.apache.ignite.internal.processors.cache,org.apache.ignite.inter
 nal.processors.cache.distributed.dht.topology,org.apache.ignite.inter
 nal.processors.cache.persistence.freelist,org.apache.ignite.internal.
 processors.cache.transactions,org.apache.ignite.internal.processors.c
 ontinuous,org.apache.ignite.internal.processors.job,org.apache.ignite
 .internal.processors.query,org.apache.ignite.internal.processors.serv
 ice,org.apache.ignite.internal.processors.task,org.apache.ignite.inte
 rnal.util,org.apache.ignite.internal.util.nio,org.apache.ignite.lang,
 org.apache.ignite.spi,org.apache.ignite.transactions";version="2.10.0
 ",org.apache.ignite.spi.tracing;uses:="org.apache.ignite,org.apache.i
 gnite.spi";version="2.10.0",org.apache.ignite.ssl;uses:="javax.cache.
 configuration,javax.net.ssl";version="2.10.0",org.apache.ignite.start
 up;uses:="org.apache.ignite.configuration,org.apache.ignite.lang";ver
 sion="2.10.0",org.apache.ignite.startup.cmdline;uses:="javax.swing";v
 ersion="2.10.0",org.apache.ignite.stream;uses:="javax.cache.processor
 ,org.apache.ignite,org.apache.ignite.cache,org.apache.ignite.lang";ve
 rsion="2.10.0",org.apache.ignite.stream.socket;uses:="org.apache.igni
 te.stream";version="2.10.0",org.apache.ignite.thread;uses:="org.apach
 e.ignite.internal,org.apache.ignite.internal.util.worker";version="2.
 10.0",org.apache.ignite.transactions;uses:="org.apache.ignite,org.apa
 che.ignite.lang";version="2.10.0",org.apache.ignite.util;uses:="org.a
 pache.ignite.cluster,org.apache.ignite.lang";version="2.10.0",org.apa
 che.ignite.util.deque;version="2.10.0",org.jsr166;version="1.0.0",org
 .mindrot;version="2.10.0",org.apache.ignite.binary;version="1.0.0",or
 g.apache.ignite.cache.affinity;version="2.10.0",org.apache.ignite.cac
 he.affinity.rendezvous;version="2.10.0",org.apache.ignite.cache;versi
 on="2.10.0",org.apache.ignite.cache.eviction;version="2.10.0",org.apa
 che.ignite.cache.eviction.fifo;version="2.10.0",org.apache.ignite.cac
 he.eviction.lru;version="2.10.0",org.apache.ignite.cache.eviction.sor
 ted;version="2.10.0",org.apache.ignite.cache.query;version="2.10.0",o
 rg.apache.ignite.cache.query.annotations;version="2.10.0",org.apache.
 ignite.cache.store;version="2.10.0",org.apache.ignite.cache.store.jdb
 c;version="2.10.0",org.apache.ignite.cache.store.jdbc.dialect;version
 ="2.10.0",org.apache.ignite.client;version="2.10.0",org.apache.ignite
 .client.util;version="2.10.0",org.apache.ignite.cluster;version="2.10
 .0",org.apache.ignite.compute;version="2.10.0",org.apache.ignite.comp
 ute.gridify.aop;version="2.10.0",org.apache.ignite.compute.gridify;ve
 rsion="2.10.0",org.apache.ignite.configuration;version="2.10.0",org.a
 pache.ignite;version="2.10.0",org.apache.ignite.events;version="2.10.
 0",org.apache.ignite.failure;version="2.10.0",org.apache.ignite.lang;
 version="2.10.0",org.apache.ignite.lifecycle;version="2.10.0",org.apa
 che.ignite.logger.java;version="2.10.0",org.apache.ignite.logger;vers
 ion="2.10.0",org.apache.ignite.maintenance;version="2.10.0",org.apach
 e.ignite.marshaller;version="2.10.0",org.apache.ignite.marshaller.jdk
 ;version="2.10.0",org.apache.ignite.messaging;version="2.10.0",org.ap
 ache.ignite.mxbean;version="2.10.0",org.apache.ignite.platform.cpp;ve
 rsion="2.10.0",org.apache.ignite.platform.dotnet;version="2.10.0",org
 .apache.ignite.platform;version="2.10.0",org.apache.ignite.plugin;ver
 sion="2.10.0",org.apache.ignite.plugin.extensions.communication;versi
 on="2.10.0",org.apache.ignite.plugin.platform;version="2.10.0",org.ap
 ache.ignite.plugin.security;version="2.10.0",org.apache.ignite.plugin
 .segmentation;version="2.10.0",org.apache.ignite.resources;version="2
 .10.0",org.apache.ignite.scheduler;version="2.10.0",org.apache.ignite
 .services;version="2.10.0",org.apache.ignite.spi.checkpoint.cache;ver
 sion="2.10.0",org.apache.ignite.spi.checkpoint;version="2.10.0",org.a
 pache.ignite.spi.checkpoint.jdbc;version="2.10.0",org.apache.ignite.s
 pi.checkpoint.noop;version="2.10.0",org.apache.ignite.spi.checkpoint.
 sharedfs;version="2.10.0",org.apache.ignite.spi.collision;version="2.
 10.0",org.apache.ignite.spi.collision.fifoqueue;version="2.10.0",org.
 apache.ignite.spi.collision.jobstealing;version="2.10.0",org.apache.i
 gnite.spi.collision.noop;version="2.10.0",org.apache.ignite.spi.colli
 sion.priorityqueue;version="2.10.0",org.apache.ignite.spi.communicati
 on;version="2.10.0",org.apache.ignite.spi.communication.tcp;version="
 2.10.0",org.apache.ignite.spi.communication.tcp.messages;version="2.1
 0.0",org.apache.ignite.spi.deployment;version="2.10.0",org.apache.ign
 ite.spi.deployment.local;version="2.10.0",org.apache.ignite.spi.disco
 very;version="2.10.0",org.apache.ignite.spi.discovery.isolated;versio
 n="2.10.0",org.apache.ignite.spi.discovery.tcp;version="2.10.0",org.a
 pache.ignite.spi.discovery.tcp.ipfinder.jdbc;version="2.10.0",org.apa
 che.ignite.spi.discovery.tcp.ipfinder.multicast;version="2.10.0",org.
 apache.ignite.spi.discovery.tcp.ipfinder.sharedfs;version="2.10.0",or
 g.apache.ignite.spi.discovery.tcp.ipfinder;version="2.10.0",org.apach
 e.ignite.spi.discovery.tcp.ipfinder.vm;version="2.10.0",org.apache.ig
 nite.spi.discovery.tcp.messages;version="2.10.0",org.apache.ignite.sp
 i.encryption;version="2.10.0",org.apache.ignite.spi.encryption.keysto
 re;version="2.10.0",org.apache.ignite.spi.encryption.noop;version="2.
 10.0",org.apache.ignite.spi.eventstorage;version="2.10.0",org.apache.
 ignite.spi.eventstorage.memory;version="2.10.0",org.apache.ignite.spi
 ;version="2.10.0",org.apache.ignite.spi.failover.always;version="2.10
 .0",org.apache.ignite.spi.failover;version="2.10.0",org.apache.ignite
 .spi.failover.jobstealing;version="2.10.0",org.apache.ignite.spi.fail
 over.never;version="2.10.0",org.apache.ignite.spi.indexing;version="2
 .10.0",org.apache.ignite.spi.indexing.noop;version="2.10.0",org.apach
 e.ignite.spi.loadbalancing.adaptive;version="2.10.0",org.apache.ignit
 e.spi.loadbalancing;version="2.10.0",org.apache.ignite.spi.loadbalanc
 ing.roundrobin;version="2.10.0",org.apache.ignite.spi.loadbalancing.w
 eightedrandom;version="2.10.0",org.apache.ignite.spi.metric;version="
 2.10.0",org.apache.ignite.spi.metric.jmx;version="2.10.0",org.apache.
 ignite.spi.metric.log;version="2.10.0",org.apache.ignite.spi.metric.n
 oop;version="2.10.0",org.apache.ignite.spi.systemview;version="2.10.0
 ",org.apache.ignite.spi.systemview.view;version="2.10.0",org.apache.i
 gnite.spi.tracing;version="2.10.0",org.apache.ignite.ssl;version="2.1
 0.0",org.apache.ignite.startup;version="2.10.0",org.apache.ignite.sta
 rtup.cmdline;version="2.10.0",org.apache.ignite.stream.socket;version
 ="2.10.0",org.apache.ignite.stream;version="2.10.0",org.apache.ignite
 .thread;version="2.10.0",org.apache.ignite.transactions;version="2.10
 .0",org.apache.ignite.util;version="2.10.0",org.apache.ignite.util.de
 que;version="2.10.0",org.jsr166;version="2.10.0"
Bundle-Name: ignite-core
Implementation-Title: ignite-core
Bundle-Description: Java-based middleware for in-memory processing of 
 big data in a distributed environment.
Implementation-Version: 2.10.0
Embed-Directory: lib
Specification-Vendor: The Apache Software Foundation
Bundle-ManifestVersion: 2
Bundle-Vendor: The Apache Software Foundation
Tool: Bnd-3.5.0.201709291849
Implementation-Vendor: The Apache Software Foundation
Bundle-Version: 2.10.0
Embed-Transitive: false
Created-By: Apache Maven Bundle Plugin
Build-Jdk: 1.8.0_241
Specification-Version: 2.10.0

